name: CI/CD - Backoffice Backend Service

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [main, homolog]
  push:
    branches: [main, homolog]

env:
  # AWS Configuration
  AWS_REGION: us-east-2
  EKS_CLUSTER_NAME_HML: hml
  EKS_CLUSTER_NAME_PRD: prd

  # Database Configuration
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: backoffice_test
  POSTGRES_HOST: localhost
  POSTGRES_PORT: 5432

  # RabbitMQ Configuration
  RABBITMQ_DEFAULT_USER: guest
  RABBITMQ_DEFAULT_PASS: guest
  RABBITMQ_PORT: 5672

  # Application Configuration
  JWT_SECRET: test_secret
  JWT_EXPIRATION: "3600"
  OTEL_EXPORTER_OTLP_ENDPOINT: http://localhost:4318

jobs:
  build:
    name: Build NestJS
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x, 22.x]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Patch package.json for CI
        env:
          NPM_CI_TOKEN: ${{ secrets.NPM_CI_TOKEN }}
        run: |
          cp package.json package.json.bak
          cp package-lock.json package-lock.json.bak
          sed -i "s#git+ssh://git@github\.com[:/]#git+https://${NPM_CI_TOKEN}@github.com/#g" package.json package-lock.json

      - run: npm ci
      - name: Restore package files
        run: |
          mv package.json.bak package.json
          mv package-lock.json.bak package-lock.json
      - run: npm run build

  homologation:
    name: Deploy para Homologação
    needs: [build]
    if: ${{ github.ref == 'refs/heads/homolog' && github.event_name == 'push' }}
    runs-on: ubuntu-latest
    steps:
      - name: Set short git commit SHA
        id: commit
        uses: prompt/actions-commit-hash@v2

      - name: Checkout do repositório
        uses: actions/checkout@v3

      - name: Instalar kubectl
        uses: azure/setup-kubectl@v2.0
        with:
          version: "v1.24.0"

      - name: Configurar credenciais AWS
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID_CI }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_CI }}
          aws-region:            ${{ env.AWS_REGION }}

      - name: Login no ECR
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }} \
            | docker login --username AWS --password-stdin 321711906762.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          registries: "267316525040"

      - name: Build & Push da imagem
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ steps.commit.outputs.short }}
          REPOSITORY: backoffice-backend-service
          NPM_CI_TOKEN: ${{ secrets.NPM_CI_TOKEN }}
        run: |
          docker build -t $REGISTRY/$REPOSITORY:$IMAGE_TAG --build-arg GITHUB_TOKEN=${NPM_CI_TOKEN} --target production .
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Configurar cliente Kubernetes
        uses: silverlyra/setup-aws-eks@v0.1.1
        with:
          cluster: ${{ env.EKS_CLUSTER_NAME_HML }}

      - name: Instalar Helm
        run: curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

      - name: Deploy com Helm
        run: |
          cd helm/hml/backoffice-backend-service-hml/
          sed -i.bak "s|latest|${{ steps.commit.outputs.short }}|g" values.yaml
          helm upgrade backoffice-backend-service-hml . -f values.yaml -n backoffice-hml -i --force

  production:
    name: Deploy para Produção
    needs: [build]
    if: ${{ github.ref == 'refs/heads/main' && github.event_name == 'push' }}
    runs-on: ubuntu-latest
    steps:
      - name: Set short git commit SHA
        id: commit
        uses: prompt/actions-commit-hash@v2

      - name: Checkout do repositório
        uses: actions/checkout@v3

      - name: Instalar kubectl
        uses: azure/setup-kubectl@v2.0
        with:
          version: "v1.24.0"

      - name: Configurar credenciais AWS
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID_CI_PRD }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_CI_PRD }}
          aws-region:            ${{ env.AWS_REGION }}

      - name: Login no ECR
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }} \
            | docker login --username AWS --password-stdin 321711906762.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com

      - name: Build & Push da imagem
        env:
          IMAGE_TAG: ${{ steps.commit.outputs.short }}
          NPM_CI_TOKEN: ${{ secrets.NPM_CI_TOKEN }}
        run: |
          docker build -t backoffice-backend-service:$IMAGE_TAG --build-arg GITHUB_TOKEN=${NPM_CI_TOKEN} --target production .
          docker tag backoffice-backend-service:$IMAGE_TAG 321711906762.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/backoffice-backend-service:$IMAGE_TAG
          docker push 321711906762.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/backoffice-backend-service:$IMAGE_TAG

      - name: Configurar cliente Kubernetes
        uses: silverlyra/setup-aws-eks@v0.1.1
        with:
          cluster: ${{ env.EKS_CLUSTER_NAME_PRD }}

      - name: Instalar Helm
        run: curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

      - name: Deploy com Helm
        run: |
          cd helm/prd/backoffice-backend-service-prd/
          sed -i.bak "s|latest|${{ steps.commit.outputs.short }}|g" values.yaml
          helm upgrade backoffice-backend-service-prd . -f values.yaml -n backoffice-prd -i --force
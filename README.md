# Petrus - NestJS Boilerplate com Arquitetura Limpa

Um boilerplate robusto para aplicações NestJS seguindo os princípios de Arquitetura Limpa (Clean Architecture) e Domain-Driven Design (DDD).

## 🏗️ Arquitetura

Este projeto implementa uma arquitetura em camadas inspirada na Clean Architecture e nos princípios de DDD:

```
src/
├── core/                       # Núcleo da aplicação (independente de frameworks)
│   ├── domain/                 # Entidades e regras de negócio
│   │   └── events/             # Eventos de domínio
│   ├── application/            # Casos de uso da aplicação
│   │   ├── services/           # Serviços que orquestram casos de uso
│   │   └── use-cases/          # Implementações de casos de uso específicos
│   ├── ports/                  # Interfaces/portas para adaptadores
│   └── base/                   # Classes base como AggregateRoot
├── infrastructure/             # Adaptadores para tecnologias externas
│   ├── aws/                    # Serviços AWS (SQS, SNS, SES)
│   ├── events/                 # Sistema de eventos
│   ├── messaging/              # Serviços de mensageria (RabbitMQ)
│   ├── prisma/                 # Configuração do Prisma ORM
│   └── repositories/           # Implementações de repositórios
└── modules/                    # Módulos NestJS
    ├── auth/                   # Módulo de autenticação
    ├── users/                  # Módulo de usuários
    └── tasks/                  # Módulo de tarefas
local/                          # Arquivos de configuração para ambiente local
└── prometheus.yml              # Configuração do Prometheus
docker-compose.yml          # Configuração do Docker Compose
```

### Camadas da Arquitetura

1. **Core (Núcleo)**: Contém toda a lógica de negócio independente de frameworks:
   - **Domain**: Entidades, enums, objetos de valor e eventos de domínio
   - **Application**: Casos de uso e serviços da aplicação
   - **Ports**: Interfaces para adaptadores externos

2. **Infrastructure (Infraestrutura)**: Implementações concretas de adaptadores:
   - **Repositories**: Implementações de repositórios com Prisma
   - **Services**: Serviços externos ou de infraestrutura
   - **Messaging**: Integrações com sistemas de mensageria
   - **AWS**: Integrações com serviços AWS

3. **Modules (Módulos)**: Organização específica do NestJS:
   - **Controllers**: Endpoints da API
   - **Services**: Serviços que conectam controllers com casos de uso
   - **DTOs**: Objetos de transferência de dados para validação

4. **Local**: Arquivos de configuração para ambiente local:
   - **docker-compose.yml**: Configuração dos containers Docker
   - **prometheus.yml**: Configuração do Prometheus para métricas

### Fluxo de Dados

O fluxo de dados segue a regra da dependência da Clean Architecture:

1. **Request HTTP** → **Controller** → **Service do Módulo** → **Service da Aplicação** → **Caso de Uso** → **Entidade de Domínio**
2. **Entidade de Domínio** → **Repositório (interface)** → **Implementação do Repositório** → **Banco de Dados**

### Casos de Uso

Os casos de uso representam as operações de negócio que a aplicação pode realizar. Cada caso de uso:

1. Recebe dados de entrada (DTO)
2. Aplica regras de negócio usando entidades de domínio
3. Interage com repositórios (através de interfaces)
4. Retorna dados de saída

Exemplo de caso de uso para criação de tarefa:

```typescript
export class CreateTaskUseCase {
  constructor(
    private taskRepository: TaskRepositoryPort,
    private eventPublisher: EventPublisherService
  ) {}

  async execute(input: CreateTaskInput): Promise<Task> {
    // Criar a entidade de domínio
    const task = new Task(
      uuidv4(),
      input.userId,
      input.title,
      input.description || '',
      TaskStatus.PENDING,
    );
    
    // Persistir no repositório
    await this.taskRepository.save(task);
    
    // Publicar eventos de domínio
    await this.eventPublisher.publishAll(task.domainEvents);
    
    // Limpar eventos publicados
    task.clearEvents();
    
    return task;
  }
}
```

## 🚀 Funcionalidades

- **Autenticação**: Login e registro com JWT
- **Gerenciamento de Usuários**: CRUD completo
- **Gerenciamento de Tarefas**: CRUD com regras de negócio
- **Autorização baseada em papéis**: Administradores e usuários comuns
- **Validação de Entrada**: Usando class-validator
- **Documentação da API**: Swagger OpenAPI
- **Banco de Dados**: PostgreSQL com Prisma ORM
- **Observabilidade**: OpenTelemetry, Prometheus e Grafana
- **Mensageria**: RabbitMQ, AWS SQS e SNS
- **Envio de Emails**: AWS SES
- **Eventos de Domínio**: Sistema de eventos para desacoplamento

## 🔐 Autenticação e Autorização

### Visão Geral

A API implementa um sistema de autenticação baseado em JWT (JSON Web Token). Para facilitar o desenvolvimento e testes, **atualmente os endpoints estão configurados sem a obrigatoriedade de autenticação**, exceto o endpoint de validação que serve como exemplo.

#### Resumo Rápido do Sistema de Autenticação

- **Tecnologia**: JWT (JSON Web Token) com Passport.js
- **Estratégia Principal**: Bearer Token via HTTP Authorization Header
- **Biblioteca Principal**: `@nestjs/jwt` e `passport-jwt`
- **Duração do Token**: Configurável via variável de ambiente `JWT_EXPIRATION`
- **Endpoints Públicos**: Login (`/auth/login`) e Registro (`/auth/register`)
- **Endpoint de Validação**: `/auth/validate` (exige autenticação)
- **Sistema de Roles**: Implementado via guard de roles (`RolesGuard`)
- **Roles Disponíveis**: `USER` (padrão) e `ADMIN`

#### Fluxo de Autenticação

1. Usuário se registra via endpoint `/auth/register`
2. Usuário faz login via endpoint `/auth/login` e recebe um token JWT
3. Usuário inclui o token nos cabeçalhos das requisições subsequentes
4. Em endpoints protegidos, o `JwtAuthGuard` valida o token e extrai as informações do usuário
5. Em endpoints com restrição de roles, o `RolesGuard` verifica se o usuário tem permissões adequadas

Para ativar a autenticação em todos os endpoints, siga as instruções na seção "Implementação da Autenticação nos Controladores".

### Como Usar a Autenticação

#### Obter o Token JWT
1. Para obter um token JWT, use o endpoint `/api/v1/api/v1/auth/login` com seu email e senha:
   ```json
   {
     "email": "<EMAIL>",
     "password": "senha123"
   }
   ```

2. A resposta incluirá um token JWT:
   ```json
   {
     "user": {
       "id": "uuid-do-usuario",
       "email": "<EMAIL>",
       "name": "Nome do Usuário",
       "role": "USER"
     },
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
   }
   ```

3. Para usar o token, inclua-o no cabeçalho das requisições:
   ```
   Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

#### Validar o Token JWT
- Para verificar se um token JWT é válido, use o endpoint `/api/v1/api/v1/auth/validate`
- Este é o único endpoint que exige autenticação por padrão
- Se o token for válido, você receberá informações sobre o usuário

### Testando Autenticação com Ferramentas

#### Usando Curl

Registro de um novo usuário:
```bash
curl -X POST http://localhost:3000/api/v1/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Usuário Teste",
    "email": "<EMAIL>",
    "password": "senha123"
  }'
```

Login e obtenção do token:
```bash
curl -X POST http://localhost:3000/api/v1/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "senha123"
  }'
```

Validação do token:
```bash
curl -X GET http://localhost:3000/api/v1/api/v1/auth/validate \
  -H "Authorization: Bearer SEU_TOKEN_JWT"
```

Acessando um endpoint protegido:
```bash
curl -X GET http://localhost:3000/api/v1/api/v1/users \
  -H "Authorization: Bearer SEU_TOKEN_JWT"
```

#### Usando Postman

1. **Configuração da Coleção**:
   - Crie uma nova coleção para sua API
   - Nas configurações da coleção, vá para a aba "Variables"
   - Adicione uma variável `token` que armazenará seu JWT

2. **Login e Armazenamento Automático do Token**:
   - Crie uma requisição POST para o endpoint de login
   - Em "Tests", adicione o seguinte script:
     ```javascript
     if (pm.response.code === 200) {
       var jsonData = pm.response.json();
       pm.collectionVariables.set("token", jsonData.access_token);
     }
     ```

3. **Uso do Token nas Requisições**:
   - Para qualquer requisição que precise de autenticação
   - Na aba "Authorization", selecione o tipo "Bearer Token"
   - No campo "Token", insira `{{token}}`

4. **Validação do Token**:
   - Crie uma requisição GET para o endpoint de validação
   - Use a mesma configuração de autorização

Com esta configuração, após fazer login uma vez, todas as requisições subsequentes usarão automaticamente o token armazenado.

### Implementação da Autenticação nos Controladores

Para implementar a autenticação obrigatória em produção, siga estas orientações:

#### Endpoints que Exigem Apenas Autenticação
Adicione os decorators `@UseGuards(JwtAuthGuard)` e `@ApiBearerAuth()` nos métodos:

```typescript
@Get('meu-endpoint')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
async meuMetodo() {
  // seu código
}
```

#### Endpoints que Exigem Autenticação e Permissões Específicas
Adicione os decorators `@UseGuards(JwtAuthGuard, RolesGuard)`, `@Roles(Role.ADMIN)` e `@ApiBearerAuth()`:

```typescript
@Get('endpoint-admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@ApiBearerAuth()
async metodoAdmin() {
  // seu código
}
```

### Controladores sem Autenticação Obrigatória

Os seguintes controladores atualmente permitem acesso sem autenticação, mas devem ser protegidos em produção:

1. **UsersController** - Endpoints para gerenciamento de usuários
2. **TasksController** - Endpoints para gerenciamento de tarefas

### Melhores Práticas de Segurança

1. **Ambiente de Produção**: Ative a autenticação obrigatória em todos os endpoints exceto login e registro
2. **Validação de Dados**: Valide todos os dados de entrada mesmo sem autenticação
3. **Proteção de Rotas**: Implemente proteção de rotas no frontend também
4. **Evite IDs de Teste**: Remova IDs fixos como `teste-user-id` antes de ir para produção
5. **Testes de Segurança**: Realize testes de penetração antes do lançamento

### Registro de Novos Usuários

Para registrar um novo usuário, use o endpoint `/api/v1/api/v1/auth/register`:

```json
{
  "name": "Nome Completo",
  "email": "<EMAIL>",
  "password": "senha123"
}
```

Por padrão, todos os usuários são criados com a role `USER`. Para criar usuários com a role `ADMIN`, você precisará alterar manualmente o registro ou criar uma rota específica para isso.

### Personalização da Autenticação

#### Criando Guards Personalizados

Para criar uma nova guarda de autenticação personalizada:

1. Crie um arquivo no diretório `src/modules/auth/guards`:

```typescript
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class MeuGuardPersonalizado implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    // Lógica de autenticação personalizada
    return true; // ou false se a autenticação falhar
  }
}
```

2. Use o guard em seus controllers:

```typescript
@UseGuards(MeuGuardPersonalizado)
@Get('endpoint-protegido')
async meuEndpoint() {
  // ...
}
```

#### Implementando Novas Estratégias

O sistema atual usa a estratégia JWT, mas você pode adicionar outras estratégias como OAuth, API Key, etc.

1. Instale as dependências necessárias (exemplo para OAuth2):
   ```bash
   npm install passport-oauth2
   ```

2. Crie uma nova estratégia em `src/modules/auth/strategies`:
   ```typescript
   import { Strategy } from 'passport-oauth2';
   import { PassportStrategy } from '@nestjs/passport';
   import { Injectable } from '@nestjs/common';

   @Injectable()
   export class OAuth2Strategy extends PassportStrategy(Strategy, 'oauth2') {
     constructor() {
       super({
         // Configuração da estratégia
       });
     }

     async validate(accessToken, refreshToken, profile, done) {
       // Validação do usuário
       return { /* dados do usuário */ };
     }
   }
   ```

3. Adicione a estratégia ao módulo de autenticação:
   ```typescript
   // src/modules/auth/auth.module.ts
   @Module({
     imports: [/* ... */],
     providers: [
       // ...
       OAuth2Strategy,
     ],
   })
   export class AuthModule {}
   ```

### Arquivo de Configuração JWT

O módulo JWT é configurado em `src/modules/auth/auth.module.ts`:

```typescript
JwtModule.registerAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => ({
    secret: configService.get('JWT_SECRET'),
    signOptions: {
      expiresIn: configService.get('JWT_EXPIRATION'),
    },
  }),
}),
```

Certifique-se de definir as variáveis de ambiente `JWT_SECRET` e `JWT_EXPIRATION` no arquivo `.env`.

## 🛠️ Tecnologias

- **NestJS**: Framework Node.js para backend
- **TypeScript**: Linguagem tipada
- **Prisma**: ORM para acesso ao banco de dados
- **PostgreSQL/MySQL/MongoDB**: Suporte a múltiplos bancos de dados
- **Docker**: Containerização
- **JWT**: Autenticação baseada em tokens
- **OpenAPI/Swagger**: Documentação da API
- **Jest**: Testes automatizados
- **ESLint/Prettier**: Formatação e linting
- **Husky**: Hooks de pré-commit para validação de código
- **OpenTelemetry**: Observabilidade padronizada
- **Prometheus/Grafana**: Métricas e dashboards
- **RabbitMQ**: Sistema de mensageria
- **AWS SDK**: Integração com serviços AWS (SQS, SNS, SES)
- **SonarCloud**: Análise de qualidade de código

## 🚦 Como Executar

### Pré-requisitos

- Node.js (v18+)
- npm ou yarn
- Docker e Docker Compose (opcional)

### Executando com Docker Compose (Recomendado)

1. Clone o repositório:
   ```bash
   git clone https://github.com/seu-usuario/backend-boilerplate.git
   cd backend-boilerplate
   ```

2. Crie um arquivo `.env` baseado no `.env.example`:
   ```bash
   cp .env.example .env
   ```

3. Inicie os containers com Docker Compose (é necessário colocar a chave para baixar a lib backoffice-backend-common):
  ```bash
  # Start SSH agent and add key
  eval $(ssh-agent)
  ssh-add ~/.ssh/id_rsa
  docker-compose up -d
   ```
> Altere `id_rsa` para o nome da chave SSH caso use uma chave customizada.

4. A aplicação estará disponível em http://localhost:3000

### Executando Localmente (sem Docker)

1. Clone o repositório:
   ```bash
   git clone https://github.com/seu-usuario/backend-boilerplate.git
   cd backend-boilerplate
   ```

2. Instale as dependências:
   ```bash
   npm install
   ```

3. Crie um arquivo `.env` baseado no `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Configure o PostgreSQL localmente e atualize a variável DATABASE_URL no .env

5. Execute as migrações do Prisma:
   ```bash
   npx prisma migrate dev
   ```

6. Inicie a aplicação em modo de desenvolvimento:
   ```bash
   npm run start:dev
   ```

7. A aplicação estará disponível em http://localhost:3000

## 📚 Documentação da API (Swagger)

A documentação da API está disponível através do Swagger UI:

- **URL**: http://localhost:3000/api/docs

Através desta interface você pode:
- Visualizar todos os endpoints disponíveis
- Testar os endpoints diretamente no navegador
- Ver os esquemas de dados e requisições
- Autenticar-se usando JWT para testar endpoints protegidos

### Como Utilizar a Autenticação no Swagger

1. Acesse o Swagger UI em http://localhost:3000/api/docs

2. Clique no botão "Authorize" no canto superior direito da página:
   ![Botão Authorize](https://i.imgur.com/xxxxx.png)

3. Na modal que se abre, insira seu token JWT (sem o prefixo "Bearer"):
   ```
   eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

4. Clique em "Authorize" e depois em "Close".

5. Agora você pode testar os endpoints protegidos diretamente no Swagger UI.

#### Obtendo um Token JWT para Testes:

1. Expanda a seção "Autenticação" 
2. Localize o endpoint POST `/api/v1/auth/login`
3. Clique em "Try it out"
4. Preencha os dados de login no formato JSON:
   ```json
   {
     "email": "<EMAIL>",
     "password": "senha123"
   }
   ```
5. Clique em "Execute"
6. Copie o token JWT da resposta (campo `access_token`)
7. Use este token no botão "Authorize" como descrito acima

## 🗂️ Estrutura do Projeto

```
backend-boilerplate/
├── prisma/                    # Configurações e migrações do Prisma
├── src/
│   ├── app.module.ts          # Módulo principal da aplicação
│   ├── app.controller.ts      # Controller principal
│   ├── app.service.ts         # Serviço principal
│   ├── main.ts                # Ponto de entrada da aplicação
│   ├── core/                  # Núcleo da aplicação
│   │   ├── domain/            # Entidades de domínio
│   │   │   ├── user.entity.ts
│   │   │   ├── task.entity.ts
│   │   │   └── events/        # Eventos de domínio
│   │   ├── application/       # Casos de uso
│   │   │   ├── services/      # Serviços da aplicação
│   │   │   └── use-cases/     # Implementações de casos de uso
│   │   ├── ports/             # Interfaces (portas) para adaptadores
│   │   │   └── repositories/  # Interfaces de repositórios
│   │   └── base/              # Classes base
│   ├── infrastructure/        # Implementações concretas de adaptadores
│   │   ├── aws/               # Integrações AWS (SQS, SNS, SES)
│   │   ├── messaging/         # Mensageria (RabbitMQ)
│   │   ├── events/            # Sistema de eventos
│   │   ├── prisma/            # Configuração do Prisma
│   │   └── repositories/      # Implementações de repositórios
│   └── modules/               # Módulos NestJS
│       ├── auth/              # Autenticação
│       ├── users/             # Usuários
│       └── tasks/             # Tarefas
├── local/                     # Configurações para ambiente local
│   ├── docker-compose.yml     # Configuração do Docker Compose
│   └── prometheus.yml         # Configuração do Prometheus
├── test/                      # Testes
├── Dockerfile                 # Configuração do Docker
├── .env.example               # Exemplo de variáveis de ambiente
├── .husky/                    # Configuração do Husky (hooks de git)
├── .eslintrc.js               # Configuração do ESLint
├── .prettierrc                # Configuração do Prettier
├── sonar-project.properties   # Configuração do SonarCloud
├── jest.config.js             # Configuração do Jest
└── README.md                  # Este arquivo
```

## 📊 Observabilidade com OpenTelemetry

A aplicação está configurada com OpenTelemetry para fornecer métricas, traces e logs estruturados.

### Configuração

A configuração da instrumentação está em `src/infrastructure/telemetry/otel.ts`.

O Prometheus está configurado em `local/prometheus.yml` para coletar métricas da aplicação através do endpoint de métricas.

### Logs Estruturados

Os logs são estruturados para facilitar a consulta e análise:

```typescript
// Exemplo de uso
logger.info('Operação executada com sucesso', {
  userId: '123',
  operation: 'createTask',
  result: 'success',
  duration: 200
});
```

### Métricas

Métricas estão disponíveis no endpoint Prometheus:

```
http://localhost:8081/metrics
```

### Rastreamento (Tracing)

Os traces são automaticamente gerados para:
- Requisições HTTP
- Operações de banco de dados (Prisma)
- Mensageria (RabbitMQ, SQS)

### Visualização

- **Jaeger UI**: http://localhost:16686 - Visualização de traces
- **Prometheus**: http://localhost:9090 - Coleta de métricas
- **Grafana**: http://localhost:3001 - Dashboards (admin/admin)

### Configuração do Docker Compose

Os serviços de observabilidade estão configurados no arquivo `local/docker-compose.yml`:

- **Jaeger**: Coletor e UI para traces
- **Prometheus**: Coleta e armazenamento de métricas
- **Grafana**: Visualização de métricas e dashboards

## 💾 Trabalhando com Prisma

### Evolução de Migrações

O Prisma facilita a criação e evolução de migrações de banco de dados:

```bash
# Criar uma nova migração
npx prisma migrate dev --name nome_da_migracao

# Aplicar migrações em ambiente de produção
npx prisma migrate deploy

# Reverter uma migração (usando shadow database)
npx prisma migrate dev --create-only
# [Edite o arquivo de migração gerado para reverter as alterações]
npx prisma migrate dev
```

### Melhores Práticas com Prisma

1. **Evolução incremental**: Crie migrações pequenas e focadas em uma única alteração.

2. **Testes de migração**: Teste suas migrações em um ambiente de desenvolvimento antes de aplicá-las em produção.

3. **Backup antes da migração**: Sempre faça backup do banco de dados antes de executar migrações em produção.

4. **Uso do Prisma Studio**: Use o Prisma Studio para visualizar e editar dados durante o desenvolvimento:
   ```bash
   npx prisma studio
   ```

5. **Documentação do esquema**: Mantenha o schema Prisma bem documentado com comentários.

## 🔄 Trabalhando com Eventos de Domínio

O projeto implementa um sistema de eventos de domínio para desacoplar componentes e implementar padrões como Event Sourcing e CQRS.

### Criando um Evento

```typescript
// Crie uma nova classe que estende DomainEvent
export class UserCreatedEvent extends DomainEvent {
  constructor(private readonly user: User) {
    super('user.created');
  }

  getData(): object {
    return {
      userId: this.user.id,
      email: this.user.email,
      createdAt: this.user.createdAt.toISOString(),
    };
  }
}
```

### Emitindo um Evento na Entidade

```typescript
// Na entidade (agregado)
class User extends AggregateRoot {
  // ...
  
  create() {
    // Lógica de negócio
    // ...
    
    // Adicionar evento
    this.addEvent(new UserCreatedEvent(this));
  }
}
```

### Consumindo um Evento

```typescript
// Crie um handler para o evento
@Injectable()
@DomainEventHandler('user.created')
export class SendWelcomeEmailHandler implements DomainEventHandler {
  constructor(private emailService: EmailService) {}
  
  async handle(event: unknown): Promise<void> {
    await this.emailService.sendWelcomeEmail(event.email);
  }
}

// Registre o handler no módulo
@Module({
  imports: [
    // ...
    registerEventHandlers(SendWelcomeEmailHandler),
  ]
})
export class UserModule {}
```

## 🗃️ Trabalhando com Diferentes Bancos de Dados

O projeto está configurado para funcionar com vários tipos de bancos de dados:

### PostgreSQL/MySQL (Relacional)

O Prisma suporta PostgreSQL e MySQL nativamente. Para usar:

1. Ajuste a URL de conexão no `.env`:
   ```
   DATABASE_URL="postgresql://user:password@localhost:5432/mydb?schema=public"
   # ou
   DATABASE_URL="mysql://user:password@localhost:3306/mydb"
   ```

2. Configure o provider no `schema.prisma`:
   ```prisma
   datasource db {
     provider = "postgresql" // ou "mysql"
     url      = env("DATABASE_URL")
   }
   ```

### MongoDB (NoSQL)

Para usar MongoDB:

1. Ajuste a URL de conexão no `.env`:
   ```
   DATABASE_URL="mongodb+srv://user:<EMAIL>/mydb?retryWrites=true&w=majority"
   ```

2. Configure o provider no `schema.prisma`:
   ```prisma
   datasource db {
     provider = "mongodb"
     url      = env("DATABASE_URL")
   }
   ```

3. Adapte os modelos para usar `@id` em vez de `@default(autoincrement())`:
   ```prisma
   model User {
     id      String @id @default(auto()) @map("_id") @db.ObjectId
     // outros campos
   }
   ```

## 📨 Mensageria e Comunicação Assíncrona

O projeto suporta diferentes sistemas de mensageria:

### RabbitMQ

```typescript
// Exemplo de produção de mensagem
await rabbitMQService.publish(
  'exchange-name',
  'routing-key',
  { message: 'Hello World' }
);

// Exemplo de consumo de mensagem
await rabbitMQService.consume(
  'queue-name',
  async (message) => {
    console.log('Mensagem recebida:', message);
  }
);
```

### AWS SQS

```typescript
// Exemplo de envio de mensagem
await sqsService.sendMessage(
  'queue-url',
  { message: 'Hello World' }
);

// Exemplo de recebimento e processamento de mensagens
await sqsService.pollQueue(
  'queue-url',
  async (message) => {
    console.log('Mensagem processada:', message);
  }
);
```

### AWS SNS

```typescript
// Exemplo de publicação de evento
await snsService.publish(
  'topic-arn',
  { event: 'UserCreated', data: { userId: '123' } },
  'User Created'
);
```

## 📧 Enviando Emails com AWS SES

```typescript
// Exemplo de envio de email
await sesService.sendEmail({
  to: '<EMAIL>',
  subject: 'Bem-vindo ao sistema',
  html: '<h1>Olá, usuário!</h1><p>Sua conta foi criada com sucesso.</p>',
});

// Exemplo de envio de email com template
await sesService.sendTemplatedEmail(
  '<EMAIL>',
  {
    templateName: 'WelcomeEmail',
    templateData: {
      name: 'João',
      activationLink: 'https://example.com/activate/token123'
    }
  }
);
```

## 🧪 Testes

Execute os testes unitários:

```bash
npm run test
```

Execute os testes e2e:

```bash
npm run test:e2e
```

Verifique a cobertura de testes:

```bash
npm run test:cov
```

### Configuração do SonarCloud

O projeto está configurado para análise de qualidade de código com SonarCloud. Para excluir arquivos da análise de cobertura de testes, utilize o arquivo `sonar-project.properties`:

```properties
# Configuração básica do projeto
sonar.projectKey=seu-usuario_backend-boilerplate
sonar.organization=sua-organizacao

# Caminho para as fontes
sonar.sources=src
sonar.tests=test

# Exclusões da análise de cobertura de código
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/*.e2e-spec.ts,src/main.ts,src/**/*.module.ts,src/infrastructure/config/**/*,src/infrastructure/telemetry/**/*,test/**/*

# Exclusões de duplicação
sonar.cpd.exclusions=**/*.spec.ts,**/*.test.ts,**/*.e2e-spec.ts

# Caminho para relatórios de cobertura de teste
sonar.javascript.lcov.reportPaths=coverage/lcov.info
```

## 📝 Exemplos de API

### Autenticação

#### Registro
```
POST /api/v1/auth/register
{
  "name": "Usuário Exemplo",
  "email": "<EMAIL>",
  "password": "Senha123!"
}
```

#### Login
```
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "Senha123!"
}
```

### Tarefas

#### Criar tarefa
```
POST /api/v1/tasks
Authorization: Bearer {seu_token}
{
  "title": "Implementar feature X",
  "description": "Detalhes da implementação..."
}
```

#### Listar tarefas do usuário
```
GET /api/v1/tasks/my-tasks
Authorization: Bearer {seu_token}
```

## 🤝 Contribuição

Contribuições são bem-vindas! Sinta-se à vontade para abrir issues ou pull requests.

## 📄 Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para mais detalhes

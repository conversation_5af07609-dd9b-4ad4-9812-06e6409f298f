# Git Flow - Backoffice Backend Service

## 📋 Visão Geral

Este documento descreve o fluxo de trabalho Git implementado para o projeto Backoffice Backend Service, baseado no modelo Gitflow e adequado às especificações de CI/CD automático.

### 🎯 Princípios do Fluxo

- **Branch principal (master)**: Código em produção com deploy automático
- **Branch de integração (HOMOLOG)**: Código para testes em homologação com deploy automático  
- **Branches de trabalho**: feature/*, fix/*, hotfix/*, release/*
- **Commits semânticos**: Todos os commits em inglês seguindo Conventional Commits
- **Deploy automático**: Baseado em merges nas branches principais (sem labels manuais)

---

## 🌳 Estrutura das Branches

### Branches Permanentes

| Branch | Descrição | Deploy Automático |
|--------|-----------|-------------------|
| `master` | Código estável em produção | ✅ Produção (PRD) |
| `HOMOLOG` | Código para testes de homologação | ✅ Homologação (HML) |

### Branches Temporárias

| Prefixo | Uso | Origem | Destino |
|---------|-----|--------|---------|
| `feature/*` | Novas funcionalidades | master | HOMOLOG |
| `fix/*` | Correção de bugs | master | HOMOLOG |
| `hotfix/*` | Correções urgentes | master | master + HOMOLOG |
| `release/*` | Preparação para produção | HOMOLOG | master |

---

## 🚀 Fluxos de Trabalho

### 1. Desenvolvimento de Funcionalidade

```bash
# 1. Criar branch de feature
git checkout master
git pull
git checkout -b feature/user-authentication

# 2. Desenvolver com commits semânticos
git commit -m "feat: add user authentication form"
git commit -m "test: add unit tests for authentication"

# 3. Push e criar PR para HOMOLOG
git push origin feature/user-authentication
# Criar PR: feature/user-authentication → HOMOLOG

# 4. Após merge na HOMOLOG, deploy automático é executado
# 5. Testar em homologação
```

### 2. Correção de Bug

```bash
# 1. Criar branch de fix
git checkout master
git pull
git checkout -b fix/login-error

# 2. Corrigir com commit semântico
git commit -m "fix: resolve login error on invalid credentials"

# 3. Push e criar PR para HOMOLOG
git push origin fix/login-error
# Criar PR: fix/login-error → HOMOLOG
```

### 3. Deploy em Produção

```bash
# 1. Criar release a partir da HOMOLOG
git checkout HOMOLOG
git pull
git checkout -b release/v1.2.0

# 2. Ajustes finais (se necessário)
git commit -m "chore: update version to 1.2.0"

# 3. Push e criar PR para master
git push origin release/v1.2.0
# Criar PR: release/v1.2.0 → master

# 4. Após merge na master, deploy em produção é automático

# 5. Sincronizar HOMOLOG (merge da release na HOMOLOG)
git checkout HOMOLOG
git pull
git merge --no-ff release/v1.2.0
git push origin HOMOLOG
```

### 4. Hotfix Urgente

```bash
# 1. Criar hotfix da master
git checkout master
git pull
git checkout -b hotfix/critical-payment-bug

# 2. Corrigir urgentemente
git commit -m "fix: resolve critical payment processing error"

# 3. Push e criar PR para master (deploy direto)
git push origin hotfix/critical-payment-bug
# Criar PR: hotfix/critical-payment-bug → master

# 4. Sincronizar HOMOLOG
git checkout HOMOLOG
git pull
git merge --no-ff hotfix/critical-payment-bug
git push origin HOMOLOG
```

---

## ✅ Commits Semânticos

### Tipos de Commit Suportados

| Tipo | Descrição | Exemplo |
|------|-----------|---------|
| `feat` | Nova funcionalidade | `feat: add user profile page` |
| `fix` | Correção de bug | `fix: resolve null pointer exception in login` |
| `chore` | Tarefas de manutenção | `chore: update dependencies` |
| `docs` | Documentação | `docs: update README with new instructions` |
| `test` | Testes | `test: add unit tests for login` |
| `refactor` | Refatoração | `refactor: simplify user validation logic` |
| `style` | Formatação | `style: fix code formatting` |
| `perf` | Performance | `perf: improve database query speed` |
| `ci` | CI/CD | `ci: update workflow configuration` |
| `build` | Build | `build: update webpack configuration` |

### Estrutura do Commit

```
<tipo>(<escopo>): <descrição curta>

<corpo (opcional)>

<rodapé (opcional)>
```

**Exemplos:**
```bash
feat(auth): implement JWT-based authentication
fix(api): correct validation error in login endpoint
chore: update npm dependencies to latest versions
```

---

## 🔄 CI/CD Automático

### Triggers de Deploy

| Evento | Ambiente | Condição |
|--------|----------|----------|
| Push na `HOMOLOG` | Homologação | Automático após merge |
| Push na `master` | Produção | Automático após merge |

### Pipeline de Validação

1. **Validação de Commits**: Verifica se commits seguem padrão semântico
2. **Build**: Compilação do código NestJS (Node 20.x e 22.x)
3. **Deploy**: Execução automática baseada na branch

### ⚠️ Importante

- **Sem labels manuais**: O sistema anterior de labels `ci-hml` e `ci-prd` foi removido
- **Deploy automático**: Ocorre automaticamente em merges nas branches principais
- **Validação obrigatória**: Commits devem seguir padrão semântico

---

## 📋 Checklist para Developers

### Antes de Começar
- [ ] Branch atualizada com `git pull`
- [ ] Criar branch com nomenclatura correta
- [ ] Configurar commitlint localmente

### Durante o Desenvolvimento
- [ ] Commits em inglês
- [ ] Seguir padrão semântico
- [ ] Testes locais funcionando
- [ ] Código revisado

### Antes do PR
- [ ] Branch de destino correta
- [ ] Descrição clara no PR
- [ ] Revisar commits do PR
- [ ] Testes passando

### Após Merge
- [ ] Testar em ambiente de destino
- [ ] Deletar branch temporária
- [ ] Verificar logs de deploy

---

## 🆘 Troubleshooting

### Commit Rejeitado
```bash
# Erro: commit não segue padrão semântico
# Solução: Use git commit --amend para corrigir
git commit --amend -m "feat: add correct semantic commit message"
```

### Deploy Não Executado
- Verificar se merge foi feito na branch correta
- Consultar logs do GitHub Actions
- Confirmar se não há erros no pipeline

### Conflitos de Merge
```bash
# Resolver conflitos manualmente
git checkout HOMOLOG
git pull
git checkout feature/minha-feature
git rebase HOMOLOG
# Resolver conflitos e continuar
git rebase --continue
```

---

## 📞 Suporte

Para dúvidas sobre o Git Flow:
1. Consulte este documento
2. Verifique os logs do GitHub Actions
3. Entre em contato com a equipe de DevOps

---

*Última atualização: Janeiro 2025*
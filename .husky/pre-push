#!/usr/bin/env sh

# Verificar se a build está funcionando
npm run build

# Rodar migrations do Prisma
npx prisma migrate deploy
# ou, para ambiente de desenvolvimento/teste:
npx prisma migrate dev --name add-deleted-at-to-users

# Push do banco de dados
npx prisma db push 

# Executar testes com cobertura antes de permitir o push
npx cross-env KEYCLOAK_BASE_URL=http://localhost:8080 npm run test:cov 

# Executar testes
npm run test
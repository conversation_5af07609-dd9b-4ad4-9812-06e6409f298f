import { BadRequestException } from '@nestjs/common';

export class UnsupportedFileTypeException extends BadRequestException {
  constructor(fileType: string) {
    super(`Unsupported file type: ${fileType}. Supported types: csv, xlsx`);
  }
}

export class FileCorruptedException extends BadRequestException {
  constructor(message: string) {
    super(`File is corrupted or invalid: ${message}`);
  }
}

export class MissingRequiredColumnsException extends BadRequestException {
  constructor(missingColumns: string[]) {
    super(`Missing required columns: ${missingColumns.join(', ')}`);
  }
}

export class FileProcessingException extends BadRequestException {
  constructor(message: string) {
    super(`Error processing file: ${message}`);
  }
}

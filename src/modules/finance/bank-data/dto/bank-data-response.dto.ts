import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsDate,
  IsOptional,
} from 'class-validator';
import {
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

export class BankDataResponseDto {
  @ApiProperty({
    description: 'ID único dos dados bancários',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Nome do banco',
    example: 'Banco do Brasil S.A.',
  })
  @IsString()
  bankName: string;

  @ApiProperty({
    description: 'Código do banco',
    example: '001',
  })
  @IsString()
  bankCode: string;

  @ApiProperty({
    description: 'Tipo da conta',
    enum: BankAccountType,
    example: BankAccountType.CHECKING,
  })
  @IsEnum(BankAccountType)
  accountType: BankAccountType;

  @ApiProperty({
    description: 'Número da agência',
    example: '1234',
  })
  @IsString()
  agencyNumber: string;

  @ApiProperty({
    description: 'Dígito da agência',
    example: '5',
    required: false,
  })
  @IsOptional()
  @IsString()
  agencyDigit?: string | null;

  @ApiProperty({
    description: 'Número da conta',
    example: '123456',
  })
  @IsString()
  accountNumber: string;

  @ApiProperty({
    description: 'Dígito da conta',
    example: '7',
  })
  @IsString()
  accountDigit: string;

  @ApiProperty({
    description: 'Nome do titular da conta',
    example: 'João da Silva',
  })
  @IsString()
  accountHolderName: string;

  @ApiProperty({
    description: 'Documento do titular da conta',
    example: '***********',
  })
  @IsString()
  accountHolderDocument: string;

  @ApiProperty({
    description: 'Chave PIX',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  pixKey?: string | null;

  @ApiProperty({
    description: 'Tipo da chave PIX',
    enum: BankPixKeyType,
    example: BankPixKeyType.EMAIL,
    required: false,
  })
  @IsOptional()
  @IsEnum(BankPixKeyType)
  pixKeyType?: BankPixKeyType | null;

  @ApiProperty({
    description: 'Indica se é um banco digital',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isDigitalBank?: boolean | null;

  @ApiProperty({
    description: 'Status dos dados bancários',
    enum: BankDataStatus,
    example: BankDataStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(BankDataStatus)
  status?: BankDataStatus | null;

  @ApiProperty({
    description: 'ID do funcionário',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsDate()
  updatedAt: Date;
}

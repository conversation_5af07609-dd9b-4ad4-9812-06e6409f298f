import {
  IsEnum,
  IsO<PERSON>al,
  IsString,
  IsUUID,
  ValidateIf,
  Validate,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { EntityType } from '@prisma/client';

@ValidatorConstraint({ name: 'CreateBankDataValidation', async: false })
class CreateBankDataConstraint implements ValidatorConstraintInterface {
  validate(_: unknown, args: ValidationArguments) {
    const dto = args.object as CreateBankDataQueryDto;

    if (dto.userId) {
      return !dto.entityType && !dto.entityId;
    } else {
      return !!dto.entityType && !!dto.entityId;
    }
  }

  defaultMessage() {
    return 'You must provide either userId, or both entityType and entityId.';
  }
}

export class CreateBankDataQueryDto {
  @IsOptional()
  @IsUUID('4')
  userId?: string;

  @ValidateIf((o) => !(o as { userId?: string }).userId)
  @IsEnum(EntityType, {
    message: 'entityType must be one of: CLIENT, COLLABORATE, SUPPLIER',
  })
  entityType?: EntityType;

  @ValidateIf((o) => !(o as { userId?: string }).userId)
  @IsString()
  @IsUUID('4')
  entityId?: string;

  @Validate(CreateBankDataConstraint)
  _customValidationTrigger: boolean;
}

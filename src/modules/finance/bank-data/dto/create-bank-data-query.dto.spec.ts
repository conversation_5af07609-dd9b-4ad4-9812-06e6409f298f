import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CreateBankDataQueryDto } from './create-bank-data-query.dto';

describe('CreateBankDataQueryDto', () => {
  describe('Valid inputs', () => {
    it('should validate with userId only', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        userId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with entityType and entityId only', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'CLIENT',
        entityId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with COLLABORATE entity type', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'COLLABORATE',
        entityId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with SUPPLIER entity type', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'SUPPLIER',
        entityId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('Invalid inputs', () => {
    it('should fail validation when no parameters provided', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {});

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const customValidationError = errors.find(
        (error) => error.property === '_customValidationTrigger',
      );
      expect(customValidationError).toBeDefined();
      expect(Object.values(customValidationError?.constraints || {})).toContain(
        'You must provide either userId, or both entityType and entityId.',
      );
    });

    it('should fail validation when both userId and entityType/entityId provided', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        userId: '550e8400-e29b-41d4-a716-************',
        entityType: 'CLIENT',
        entityId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const customValidationError = errors.find(
        (error) => error.property === '_customValidationTrigger',
      );
      expect(customValidationError).toBeDefined();
      expect(Object.values(customValidationError?.constraints || {})).toContain(
        'You must provide either userId, or both entityType and entityId.',
      );
    });

    it('should fail validation when only entityType provided', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'CLIENT',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const entityIdError = errors.find(
        (error) => error.property === 'entityId',
      );
      expect(entityIdError).toBeDefined();
    });

    it('should fail validation when only entityId provided', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const entityTypeError = errors.find(
        (error) => error.property === 'entityType',
      );
      expect(entityTypeError).toBeDefined();
    });

    it('should fail validation for invalid userId UUID', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        userId: 'invalid-uuid',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const userIdError = errors.find((error) => error.property === 'userId');
      expect(userIdError).toBeDefined();
    });

    it('should fail validation for invalid entityId UUID', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'CLIENT',
        entityId: 'invalid-uuid',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const entityIdError = errors.find(
        (error) => error.property === 'entityId',
      );
      expect(entityIdError).toBeDefined();
    });

    it('should fail validation for invalid entityType', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'INVALID_TYPE',
        entityId: 'bbbbbbbb-cccc-dddd-eeee-ffffffffffff',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const entityTypeError = errors.find(
        (error) => error.property === 'entityType',
      );
      expect(entityTypeError).toBeDefined();
      expect(Object.values(entityTypeError?.constraints || {})).toContain(
        'entityType must be one of: CLIENT, COLLABORATE, SUPPLIER',
      );
    });
  });

  describe('Custom validation logic', () => {
    it('should pass custom validation when userId is provided and entityType/entityId are not', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        userId: '550e8400-e29b-41d4-a716-************',
      });

      const errors = await validate(dto);
      const customValidationError = errors.find(
        (error) => error.property === '_customValidationTrigger',
      );
      expect(customValidationError).toBeUndefined();
    });

    it('should pass custom validation when entityType and entityId are provided and userId is not', async () => {
      const dto = plainToClass(CreateBankDataQueryDto, {
        entityType: 'CLIENT',
        entityId: 'bbbbbbbb-cccc-dddd-eeee-ffffffffffff',
      });

      const errors = await validate(dto);
      const customValidationError = errors.find(
        (error) => error.property === '_customValidationTrigger',
      );
      expect(customValidationError).toBeUndefined();
    });
  });
});

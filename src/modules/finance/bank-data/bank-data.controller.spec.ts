import { Test, TestingModule } from '@nestjs/testing';
import { BankDataController } from './bank-data.controller';
import { BankDataService } from './bank-data.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import {
  BankDataStatus,
  BankAccountType,
  BankPixKeyType,
  EntityType,
} from '@prisma/client';
import { GetBankDataQueryDto } from './dto/get-bank-data-query.dto';

describe('BankDataController', () => {
  let controller: BankDataController;

  const mockBankDataService = {
    create: jest.fn(),
    update: jest.fn(),
    findById: jest.fn(),
    findAll: jest.fn(),
  };

  const mockBankDataResponse = {
    id: 'bank-data-uuid',
    bankName: 'Banco do Brasil S.A.',
    bankCode: '001',
    accountType: BankAccountType.CHECKING,
    agencyNumber: '1234',
    agencyDigit: '5',
    accountNumber: '123456',
    accountDigit: '7',
    accountHolderName: '<PERSON>e',
    accountHolderDocument: '***********',
    pixKey: '<EMAIL>',
    pixKeyType: BankPixKeyType.EMAIL,
    isDigitalBank: false,
    status: BankDataStatus.ACTIVE,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BankDataController],
      providers: [
        {
          provide: BankDataService,
          useValue: mockBankDataService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<BankDataController>(BankDataController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('parseIntOrDefault', () => {
    it('should parse valid string numbers', () => {
      const result = controller['parseIntOrDefault']('10', 0);
      expect(result).toBe(10);
    });

    it('should return default for invalid string', () => {
      const result = controller['parseIntOrDefault']('abc', 5);
      expect(result).toBe(5);
    });

    it('should return number directly', () => {
      const result = controller['parseIntOrDefault'](15, 0);
      expect(result).toBe(15);
    });

    it('should return default for null', () => {
      const result = controller['parseIntOrDefault'](null, 10);
      expect(result).toBe(10);
    });

    it('should return default for undefined', () => {
      const result = controller['parseIntOrDefault'](undefined, 20);
      expect(result).toBe(20);
    });

    it('should return default for empty string', () => {
      const result = controller['parseIntOrDefault']('', 25);
      expect(result).toBe(25);
    });

    it('should handle negative numbers', () => {
      const result = controller['parseIntOrDefault']('-5', 0);
      expect(result).toBe(-5);
    });

    it('should handle zero string', () => {
      const result = controller['parseIntOrDefault']('0', 10);
      expect(result).toBe(0);
    });
  });

  describe('findAll', () => {
    it('should call service with default values when no query provided', async () => {
      const query = new GetBankDataQueryDto();
      const mockResponse = {
        data: [mockBankDataResponse],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockBankDataService.findAll.mockResolvedValue(mockResponse);

      const result = await controller.findAll(query);

      expect(result).toEqual(mockResponse);
      expect(mockBankDataService.findAll).toHaveBeenCalledWith({
        limit: 10,
        offset: 0, // (1-1) * 10 = 0
        query: {
          entityType: undefined,
          bankName: undefined,
          bankCode: undefined,
          accountType: undefined,
          agencyNumber: undefined,
          agencyDigit: undefined,
          accountNumber: undefined,
          accountDigit: undefined,
          accountHolderName: undefined,
          accountHolderDocument: undefined,
          pixKey: undefined,
          pixKeyType: undefined,
          isDigitalBank: undefined,
          createdAt: undefined,
          updatedAt: undefined,
          status: undefined,
          employeeId: 0,
        },
      });
    });

    it('should call service with all query parameters', async () => {
      const query: GetBankDataQueryDto = {
        page: 2,
        limit: 20,
        employeeId: 123,
        status: BankDataStatus.ACTIVE,
        entityType: EntityType.COLLABORATE,
        bankName: 'Banco do Brasil',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'João da Silva',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      };

      const mockResponse = {
        data: [mockBankDataResponse],
        total: 1,
        page: 2,
        limit: 20,
        totalPages: 1,
      };

      mockBankDataService.findAll.mockResolvedValue(mockResponse);

      const result = await controller.findAll(query);

      expect(result).toEqual(mockResponse);
      expect(mockBankDataService.findAll).toHaveBeenCalledWith({
        limit: 20,
        offset: 20, // (2-1) * 20 = 20
        query: {
          entityType: EntityType.COLLABORATE,
          bankName: 'Banco do Brasil',
          bankCode: '001',
          accountType: BankAccountType.CHECKING,
          agencyNumber: '1234',
          agencyDigit: '5',
          accountNumber: '123456',
          accountDigit: '7',
          accountHolderName: 'João da Silva',
          accountHolderDocument: '***********',
          pixKey: '<EMAIL>',
          pixKeyType: BankPixKeyType.EMAIL,
          isDigitalBank: false,
          createdAt: new Date('2023-01-01T00:00:00.000Z'),
          updatedAt: new Date('2023-01-01T00:00:00.000Z'),
          status: BankDataStatus.ACTIVE,
          employeeId: 123,
        },
      });
    });

    it('should handle string numbers in query parameters', async () => {
      const query = {
        page: 3,
        limit: 25,
        employeeId: 999,
      } as GetBankDataQueryDto;

      const mockResponse = {
        data: [],
        total: 0,
        page: 1,
        limit: 25,
        totalPages: 0,
      };

      mockBankDataService.findAll.mockResolvedValue(mockResponse);

      await controller.findAll(query);

      expect(mockBankDataService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          limit: 25,
          offset: 50, // (3-1) * 25 = 50
          query: expect.objectContaining({
            employeeId: 999,
          }),
        }),
      );
    });

    it('should handle invalid string numbers with defaults', async () => {
      const query = {
        page: 'invalid' as unknown as number,
        limit: 'invalid' as unknown as number,
        employeeId: 'not-a-number' as unknown as number,
      } as GetBankDataQueryDto;

      const mockResponse = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };

      mockBankDataService.findAll.mockResolvedValue(mockResponse);

      await controller.findAll(query);

      expect(mockBankDataService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          limit: 10, // default
          offset: 0, // (1-1) * 10 = 0 (page defaults to 1)
          query: expect.objectContaining({
            employeeId: 0, // default for invalid employeeId
          }),
        }),
      );
    });

    it('should convert date strings to Date objects', async () => {
      const query = {
        createdAt: '2023-06-15T10:30:00.000Z',
        updatedAt: '2023-06-16T15:45:00.000Z',
      } as GetBankDataQueryDto;

      const mockResponse = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };

      mockBankDataService.findAll.mockResolvedValue(mockResponse);

      await controller.findAll(query);

      expect(mockBankDataService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            createdAt: new Date('2023-06-15T10:30:00.000Z'),
            updatedAt: new Date('2023-06-16T15:45:00.000Z'),
          }),
        }),
      );
    });

    it('should handle undefined date strings', async () => {
      const query = {
        createdAt: undefined,
        updatedAt: undefined,
      } as GetBankDataQueryDto;

      const mockResponse = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };

      mockBankDataService.findAll.mockResolvedValue(mockResponse);

      await controller.findAll(query);

      expect(mockBankDataService.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          query: expect.objectContaining({
            createdAt: undefined,
            updatedAt: undefined,
          }),
        }),
      );
    });
  });
});

import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import {
  CERTIFICATE_REPOSITORY,
  ICertificateRepository,
} from '@/core/ports/repositories/certificate.repository.port';
import { IStorageProvider } from '@/core/ports/storage/storage-provider.port';
import { UpdateCertificateDto } from '../../infrastructure/dtos/update-certificate.dto';

interface UpdateCertificateUseCaseRequest {
  id: string;
  data: UpdateCertificateDto;
  file?: Express.Multer.File;
  updatedBy: string;
}

@Injectable()
export class UpdateCertificateUseCase {
  constructor(
    @Inject(CERTIFICATE_REPOSITORY)
    private readonly certificateRepository: ICertificateRepository,
    @Inject('IStorageProvider')
    private readonly storageProvider: IStorageProvider,
  ) {}

  async execute({ id, data, file }: UpdateCertificateUseCaseRequest) {
    const existingCertificate = await this.certificateRepository.findById(id);

    if (!existingCertificate) {
      throw new NotFoundException('Certificado não encontrado.');
    }

    let newFileUrl = existingCertificate.fileUrl;

    if (file) {
      // Idealmente, deletar o arquivo antigo do storage aqui
      // await this.storageProvider.delete(existingCertificate.fileUrl);

      const path = `customers/${
        existingCertificate.customerId
      }/certificates/${Date.now()}-${file.originalname}`;
      await this.storageProvider.upload(file.buffer, file.mimetype, path);
      newFileUrl = path;
    }

    // Atualiza as propriedades da entidade
    existingCertificate.category =
      data.category ?? existingCertificate.category;
    existingCertificate.type = data.type ?? existingCertificate.type;
    existingCertificate.notes = data.notes ?? existingCertificate.notes;
    existingCertificate.emissionDate = data.emissionDate
      ? new Date(data.emissionDate)
      : existingCertificate.emissionDate;
    existingCertificate.expirationDate = data.expirationDate
      ? new Date(data.expirationDate)
      : existingCertificate.expirationDate;
    existingCertificate.fileUrl = newFileUrl;
    existingCertificate.updatedAt = new Date();

    return this.certificateRepository.update(existingCertificate);
  }
}

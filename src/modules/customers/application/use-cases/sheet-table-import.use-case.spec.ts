import { Test, TestingModule } from '@nestjs/testing';
import { SheetTableImportUseCase } from './sheet-table-import.use-case';
import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import { UserRepository } from '../../../../core/ports/repositories/user-repository.interface';
import { FileReaderService } from '../../../file-reader/services/file-reader.service';
import { UsersService } from '../../../users/users.service';
import { AuthService } from '../../../auth/auth.service';

describe('SheetTableImportUseCase', () => {
  let useCase: SheetTableImportUseCase;
  let customerRepository: jest.Mocked<ICustomerRepository>;
  let userRepository: jest.Mocked<UserRepository>;
  let fileReaderService: jest.Mocked<FileReaderService>;
  let usersService: jest.Mocked<UsersService>;
  let authService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const mockCustomerRepository = {
      create: jest.fn(),
      findByEmail: jest.fn(),
      findByDocument: jest.fn(),
    };

    const mockUserRepository = {
      findByEmail: jest.fn(),
    };

    const mockFileReaderService = {
      readFile: jest.fn(),
    };

    const mockUsersService = {
      createUser: jest.fn(),
    };

    const mockAuthService = {
      generateTemporaryPassword: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SheetTableImportUseCase,
        {
          provide: 'CUSTOMER_REPOSITORY',
          useValue: mockCustomerRepository,
        },
        {
          provide: 'USER_REPOSITORY',
          useValue: mockUserRepository,
        },
        {
          provide: FileReaderService,
          useValue: mockFileReaderService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    useCase = module.get<SheetTableImportUseCase>(SheetTableImportUseCase);
    customerRepository = module.get('CUSTOMER_REPOSITORY');
    userRepository = module.get('USER_REPOSITORY');
    fileReaderService = module.get(FileReaderService);
    usersService = module.get(UsersService);
    authService = module.get(AuthService);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should have all dependencies injected', () => {
    expect(customerRepository).toBeDefined();
    expect(userRepository).toBeDefined();
    expect(fileReaderService).toBeDefined();
    expect(usersService).toBeDefined();
    expect(authService).toBeDefined();
  });
});

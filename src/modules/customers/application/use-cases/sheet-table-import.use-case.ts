import { Injectable, Inject, NotFoundException, Logger } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

import { ICustomerRepository } from '../../domain/repositories/customer.repository.interface';
import {
  Customer,
  CustomerStatus,
} from '../../domain/entities/customer.entity';
import {
  SheetTableImportRowDto,
  SheetTableImportResultDto,
} from '../../infrastructure/dtos/sheet-table-import.dto';
import { FileReaderService } from '../../../file-reader/services/file-reader.service';
import { UsersService } from '../../../users/users.service';
import { AuthService } from '../../../auth/auth.service';
import { Role } from '../../../../core/domain/role.enum';
import { UserRepository } from '../../../../core/ports/repositories/user-repository.interface';
import { DuplicateEmailError } from '../../../../infrastructure/exceptions/duplicate-email.error';
import { DuplicateCnpjError } from '../../../../infrastructure/exceptions/duplicate-cnpj.error';
import { IdentityProviderService } from '../../../../infrastructure/keycloak/keycloak-identity-provider.service';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import type { Prisma } from '@prisma/client';

export interface SheetTableImportInput {
  file: Express.Multer.File;
  createdBy: string;
}

@Injectable()
export class SheetTableImportUseCase {
  private readonly logger = new Logger(SheetTableImportUseCase.name);
  private activeUserCreations = 0;
  private readonly MAX_CONCURRENT_USER_CREATIONS = 3;

  constructor(
    @Inject('CUSTOMER_REPOSITORY')
    private readonly customerRepository: ICustomerRepository,
    @Inject('USER_REPOSITORY')
    private readonly userRepository: UserRepository,
    private readonly fileReaderService: FileReaderService,
    private readonly usersService: UsersService,
    private readonly authService: AuthService,
    @Inject('IdentityProviderService')
    private readonly keycloakIdentityProvider: IdentityProviderService,
    private readonly prisma: PrismaService,
  ) {}

  async execute(
    input: SheetTableImportInput,
  ): Promise<SheetTableImportResultDto> {
    const { file, createdBy } = input;

    this.logger.log(`Starting customer import from file: ${file.originalname}`);

    // Validar usuário criador
    const requestUser = await this.userRepository.findByEmail(createdBy);
    if (!requestUser) {
      throw new NotFoundException('Request user not found');
    }

    // Ler e processar arquivo
    const fileData = await this.fileReaderService.readFile(file, {
      skipEmptyLines: true,
      trimValues: true,
    });

    const result: SheetTableImportResultDto = {
      totalProcessed: fileData.totalRows,
      successCount: 0,
      errorCount: 0,
      errors: [],
      successfulCustomers: [],
    };

    // TRANSAÇÃO GLOBAL - Tudo ou nada
    return await this.prisma.$transaction(async (prismaTransaction) => {
      this.logger.log('Iniciando transação global para importação');

      const createdKeycloakUsers: string[] = []; // Para rollback do Keycloak

      try {
        // Processar cada linha sequencialmente dentro da transação
        for (let i = 0; i < fileData.data.length; i++) {
          const rowData = fileData.data[i];
          const rowNumber = i + 2; // +2 porque linha 1 é header

          try {
            const keycloakUserId = await this.processCustomerRowInTransaction(
              rowData,
              rowNumber,
              requestUser.id,
              result,
              prismaTransaction,
            );

            if (keycloakUserId) {
              createdKeycloakUsers.push(keycloakUserId);
            }
          } catch (error) {
            this.logger.error(`Error processing row ${rowNumber}:`, error);
            result.errorCount++;
            result.errors.push(
              `Linha ${rowNumber}: ${(error as Error).message || String(error)}`,
            );

            // Se qualquer linha falhar, lança erro para fazer rollback da transação
            throw new Error(
              `Falha na linha ${rowNumber}: ${(error as Error).message}`,
            );
          }
        }

        this.logger.log(
          `Transação concluída com sucesso. Success: ${result.successCount}, Errors: ${result.errorCount}`,
        );

        return result;
      } catch (error) {
        this.logger.error(
          'Erro na transação, fazendo rollback completo:',
          error,
        );

        // Fazer rollback dos usuários criados no Keycloak
        await this.rollbackKeycloakUsers(createdKeycloakUsers);

        // A transação do Prisma fará rollback automaticamente
        throw error;
      }
    });
  }

  private async processCustomerRowInTransaction(
    rowData: Record<string, unknown>,
    _rowNumber: number,
    createdById: string,
    result: SheetTableImportResultDto,
    _prismaTransaction: Prisma.TransactionClient, // Será usado para operações de banco dentro da transação
  ): Promise<string | null> {
    // Converter dados da linha para DTO
    const customerData = plainToClass(SheetTableImportRowDto, {
      razaoSocial: this.safeStringify(
        rowData.razaoSocial ||
          rowData['Razão Social'] ||
          rowData['razao_social'],
      ),
      cnpj: this.cleanNumericString(
        this.safeStringify(rowData.cnpj || rowData.CNPJ || ''),
      ),
      email: this.safeStringify(rowData.email || rowData.Email || '')
        .toLowerCase()
        .trim(),
      phone: this.safeStringifyOptional(
        rowData.phone || rowData.telefone || rowData.Telefone,
      ),
      url: this.safeStringifyOptional(
        rowData.url || rowData.site || rowData.Site,
      ),
      street: this.safeStringify(
        rowData.street ||
          rowData.rua ||
          rowData.endereco ||
          rowData['Endereço'],
      ),
      number: this.safeStringifyOptional(
        rowData.number || rowData.numero || rowData['Número'],
      ),
      complement: this.safeStringifyOptional(
        rowData.complement || rowData.complemento || rowData.Complemento,
      ),
      neighborhood: this.safeStringifyOptional(
        rowData.neighborhood || rowData.bairro || rowData.Bairro,
      ),
      city: this.safeStringify(
        rowData.city || rowData.cidade || rowData.Cidade,
      ),
      state: this.safeStringify(
        rowData.state || rowData.estado || rowData.Estado || '',
      ).toUpperCase(),
      zipCode: this.cleanNumericString(
        this.safeStringify(rowData.zipCode || rowData.cep || rowData.CEP || ''),
      ),
    });

    // Validar dados
    const validationErrors = await validate(customerData);
    if (validationErrors.length > 0) {
      const errorMessages = validationErrors
        .map(this.extractConstraintMessages)
        .join('; ');
      throw new Error(`Dados inválidos: ${errorMessages}`);
    }

    // Verificar duplicatas
    await this.checkDuplicates(customerData);

    // Criar usuário (com controle de concorrência)
    const createdUser =
      await this.createUserWithConcurrencyControl(customerData);

    // Criar cliente
    await this.createCustomer(customerData, createdUser.id, createdById);

    // Enviar email de confirmação (não bloqueia se falhar)
    try {
      await this.sendConfirmationEmail(createdUser.email);
    } catch (emailError) {
      this.logger.warn(
        `Falha no envio de email para ${createdUser.email}:`,
        emailError,
      );
      // Continua mesmo se email falhar
    }

    result.successCount++;
    result.successfulCustomers.push(
      `${customerData.razaoSocial} (${customerData.email})`,
    );

    // Retornar o keycloakId para possível rollback
    return createdUser.keycloakId || null;
  }

  private async checkDuplicates(
    customerData: SheetTableImportRowDto,
  ): Promise<void> {
    // Verificar CNPJ duplicado
    const existingCustomerByCnpj = await this.customerRepository.findByDocument(
      customerData.cnpj,
    );
    if (existingCustomerByCnpj) {
      throw new DuplicateCnpjError(customerData.cnpj, 'cliente');
    }

    // Verificar email duplicado
    const existingCustomerByEmail = await this.customerRepository.findByEmail(
      customerData.email,
    );
    if (existingCustomerByEmail) {
      throw new DuplicateEmailError(customerData.email, 'cliente');
    }

    // Verificar se já existe usuário com o email
    const existingUser = await this.usersService
      .findByEmail(customerData.email)
      .catch((error) => {
        if (error instanceof NotFoundException) {
          return null;
        }
        throw error; // Re-throw unexpected errors
      });

    if (existingUser) {
      throw new DuplicateEmailError(customerData.email, 'usuário');
    }
  }

  private async createUserWithConcurrencyControl(
    customerData: SheetTableImportRowDto,
  ) {
    // Aguardar se já temos muitas criações simultâneas
    while (this.activeUserCreations >= this.MAX_CONCURRENT_USER_CREATIONS) {
      await new Promise((resolve) => setTimeout(resolve, 100)); // Aguardar 100ms
    }

    this.activeUserCreations++;

    try {
      const user = await this.createUserForCustomer(customerData);
      return user;
    } finally {
      this.activeUserCreations--;
    }
  }

  private async createUserForCustomer(customerData: SheetTableImportRowDto) {
    this.logger.debug(`Tentando criar usuário para: ${customerData.email}`);

    const temporaryPassword = this.generateRandomPassword();
    this.logger.debug(`Senha temporária gerada para ${customerData.email}`);

    const result = await this.usersService.create({
      name: customerData.razaoSocial,
      email: customerData.email,
      password: temporaryPassword,
      role: Role.CUSTOMER_VIEWER,
    });

    this.logger.debug(`Usuário criado com sucesso: ${customerData.email}`);
    return result;
  }

  private async createCustomer(
    customerData: SheetTableImportRowDto,
    userId: string,
    createdById: string,
  ): Promise<Customer> {
    const customer: Customer = {
      uuid: uuidv4(),
      razaoSocial: customerData.razaoSocial,
      cnpj: customerData.cnpj,
      email: customerData.email,
      phone: customerData.phone,
      address: {
        street: customerData.street,
        number: customerData.number,
        complement: customerData.complement,
        neighborhood: customerData.neighborhood,
        city: customerData.city,
        state: customerData.state,
        zipCode: customerData.zipCode,
      },
      status: CustomerStatus.PENDING,
      userId,
      url: customerData.url || '',
      createdBy: createdById,
      updatedBy: createdById,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return await this.customerRepository.create(customer);
  }

  private async sendConfirmationEmail(email: string): Promise<void> {
    try {
      await this.authService.forgotPassword({ email });
    } catch (error) {
      this.logger.warn(`Failed to send confirmation email to ${email}:`, error);
      // Não falha a importação por causa do email
    }
  }

  private extractConstraintMessages = (error: {
    constraints?: Record<string, string>;
  }): string => Object.values(error.constraints || {}).join(', ');

  private safeStringify(value: unknown): string {
    if (value === null || value === undefined) {
      return '';
    }

    if (typeof value === 'string') {
      return value;
    }

    if (typeof value === 'number') {
      return value.toString();
    }

    if (typeof value === 'boolean') {
      return value.toString();
    }

    // Para objetos, arrays, etc., retorna string vazia
    // Evita "[object Object]"
    return '';
  }

  private safeStringifyOptional(value: unknown): string | undefined {
    const result = this.safeStringify(value);
    // Se o resultado for string vazia, retorna undefined para campos opcionais
    return result === '' ? undefined : result;
  }

  private cleanNumericString(value: string): string {
    return value.replace(/\D/g, ''); // Remove tudo que não é dígito
  }

  private async rollbackUser(userId: string): Promise<void> {
    try {
      // Buscar dados do usuário para obter keycloakId
      const user = await this.usersService.findById(userId);

      // Deletar usuário do banco local (soft delete)
      await this.usersService.delete(userId);
      this.logger.debug(`Usuário ${userId} removido do banco local`);

      // Se tiver keycloakId, remover do Keycloak também
      if (user.keycloakId) {
        try {
          await this.keycloakIdentityProvider.deleteUser(user.keycloakId);
          this.logger.debug(
            `Usuário ${userId} removido do Keycloak (ID: ${user.keycloakId})`,
          );
        } catch (keycloakError) {
          this.logger.error(
            `Erro ao remover usuário do Keycloak:`,
            keycloakError,
          );
          // Não falha o rollback por causa do Keycloak
        }
      }
    } catch (error) {
      this.logger.error(`Erro ao fazer rollback do usuário ${userId}:`, error);
      throw error;
    }
  }

  private async rollbackKeycloakUsers(
    keycloakUserIds: string[],
  ): Promise<void> {
    this.logger.log(
      `Fazendo rollback de ${keycloakUserIds.length} usuários do Keycloak`,
    );

    for (const keycloakUserId of keycloakUserIds) {
      try {
        await this.keycloakIdentityProvider.deleteUser(keycloakUserId);
        this.logger.debug(
          `Usuário ${keycloakUserId} removido do Keycloak durante rollback`,
        );
      } catch (keycloakError) {
        this.logger.error(
          `Erro ao remover usuário ${keycloakUserId} do Keycloak durante rollback:`,
          keycloakError,
        );
        // Continua tentando remover os outros usuários
      }
    }
  }

  private generateRandomPassword(): string {
    const length = 12;
    const charset =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********!@#$%&*';
    let password = '';

    // Garantir pelo menos um de cada tipo
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const symbols = '!@#$%&*';

    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Preencher o resto aleatoriamente
    for (let i = password.length; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Embaralhar a senha para não ter padrão previsível
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }
}

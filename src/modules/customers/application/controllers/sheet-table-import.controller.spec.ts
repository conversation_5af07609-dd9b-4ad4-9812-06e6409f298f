import { Test, TestingModule } from '@nestjs/testing';
import { SheetTableImportController } from './sheet-table-import.controller';
import { SheetTableImportUseCase } from '../use-cases/sheet-table-import.use-case';
import { ConfigService } from '@nestjs/config';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';

describe('SheetTableImportController', () => {
  let controller: SheetTableImportController;
  let sheetTableImportUseCase: jest.Mocked<SheetTableImportUseCase>;

  beforeEach(async () => {
    const mockSheetTableImportUseCase = {
      execute: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const mockJwtAuthGuard = {
      canActivate: jest.fn().mockReturnValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [SheetTableImportController],
      providers: [
        {
          provide: SheetTableImportUseCase,
          useValue: mockSheetTableImportUseCase,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: JwtAuthGuard,
          useValue: mockJwtAuthGuard,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    controller = module.get<SheetTableImportController>(
      SheetTableImportController,
    );
    sheetTableImportUseCase = module.get(SheetTableImportUseCase);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should have SheetTableImportUseCase injected', () => {
    expect(sheetTableImportUseCase).toBeDefined();
  });
});

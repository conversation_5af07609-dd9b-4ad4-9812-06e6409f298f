import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Request,
  HttpStatus,
  UseGuards,
  Logger,
  UnsupportedMediaTypeException,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { SheetTableImportUseCase } from '../use-cases/sheet-table-import.use-case';
import { SheetTableImportResultDto } from '../../infrastructure/dtos/sheet-table-import.dto';

interface AuthenticatedRequest extends Request {
  user: {
    preferred_username: string;
    email: string;
  };
}

@ApiTags('Customers sheet table import')
@Controller('core/customers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SheetTableImportController {
  private readonly logger = new Logger(SheetTableImportController.name);

  constructor(
    private readonly sheetTableImportUseCase: SheetTableImportUseCase,
  ) {}

  @Post('sheet-table-import')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({
    summary: 'Importar clientes em massa via planilha',
    description: `
      Importa clientes em massa através de uma planilha Excel (.xlsx) ou CSV (.csv).
      
      REQUISITOS DO ARQUIVO:
      - Formato: Excel (.xlsx) ou CSV (.csv)
      - Tamanho máximo: 10MB
      - Primeira linha: DEVE conter os cabeçalhos das colunas
      - Ordem das colunas: Não importa, o sistema identifica pelos nomes dos cabeçalhos
      - Codificação: UTF-8 (recomendado para CSV)
      
      COLUNAS ACEITAS (múltiplas variações de nomes):
      
      Razão Social (OBRIGATÓRIO):
      - razaoSocial, Razão Social, razao_social, nome, name
      
      CNPJ (OBRIGATÓRIO):
      - cnpj, CNPJ
      - Formato aceito: com ou sem pontuação (12.345.678/0001-90 ou 12345678000190)
      
      Email (OBRIGATÓRIO):
      - email, Email
      - Será usado para criar o usuário de acesso
      
      Telefone (OPCIONAL):
      - phone, telefone, Telefone
      
      Endereço (OPCIONAL):
      - street, rua, endereco, Endereço
      
      Número (OPCIONAL):
      - number, numero, Número
      
      Complemento (OPCIONAL):
      - complement, complemento, Complemento
      
      Bairro (OPCIONAL):
      - neighborhood, bairro, Bairro
      
      Cidade (OBRIGATÓRIO):
      - city, cidade, Cidade
      
      Estado (OBRIGATÓRIO):
      - state, estado, Estado, uf, UF
      
      CEP (OBRIGATÓRIO):
      - zipCode, cep, CEP
      - Formato aceito: com ou sem pontuação (01234-567 ou 01234567)
      
      Website (OPCIONAL):
      - url, site, Site, website
      
      EXEMPLO DE PLANILHA CSV:
      
      Primeira linha (cabeçalhos):
      razaoSocial,cnpj,email,telefone,street,city,state,zipCode
      
      Dados:
      "Empresa ABC Ltda","12.345.678/0001-90","<EMAIL>","(11) 1234-5678","Rua das Flores, 123","São Paulo","SP","01234-567"
      "Empresa XYZ S.A.","98.765.432/0001-10","<EMAIL>","(21) 9876-5432","Av. Paulista, 456","Rio de Janeiro","RJ","98765-432"
      
      EXEMPLO ALTERNATIVO (nomes em português):
      
      Cabeçalhos:
      Razão Social,CNPJ,Email,Telefone,Endereço,Cidade,Estado,CEP
      
      Dados:
      "Empresa ABC Ltda","12345678000190","<EMAIL>","11 1234-5678","Rua das Flores, 123","São Paulo","SP","01234567"
      
      VALIDAÇÕES REALIZADAS:
      1. Arquivo: Formato, tamanho e integridade
      2. Dados obrigatórios: Razão social, email, CNPJ, endereço, cidade, estado e CEP
      3. Formato de email: Validação de formato válido
      4. CNPJ: Validação de formato e dígitos verificadores
      5. Duplicatas: Verificação de email e CNPJ já existentes
      6. Usuário: Validação do usuário que está fazendo a importação
      
      FORMATAÇÃO AUTOMÁTICA:
      - CNPJ: Remove automaticamente pontuação (12.345.678/0001-90 → 12345678000190)
      - CEP: Remove automaticamente pontuação (01234-567 → 01234567)
      - Email: Converte automaticamente para minúsculas
      
      PROCESSO DE IMPORTAÇÃO:
      1. Validação do usuário: Verifica se o usuário do JWT existe no sistema
      2. Leitura da planilha: Processa o arquivo e extrai os dados
      3. Validação dos dados: Valida cada linha da planilha
      4. Criação de usuários: Cria usuários de referência para cada cliente
      5. Criação de clientes: Cria os registros de clientes no banco
      6. Relatório: Gera relatório com sucessos e erros
      
      OBSERVAÇÕES IMPORTANTES:
      - Headers/Cabeçalhos: OBRIGATÓRIO - A primeira linha deve conter os nomes das colunas
      - Case-insensitive: email, Email e EMAIL são aceitos
      - Flexível: Aceita variações como razaoSocial, Razão Social, razao_social
      - Para cada cliente importado, será criado um usuário com role CUSTOMER_VIEWER
      - A senha temporária será enviada por email
      - Clientes duplicados (mesmo CNPJ ou email) serão rejeitados
      - O processo continua mesmo se algumas linhas falharem
      - Erros de validação: Continuam processando outras linhas
      - Erros críticos: Interrompem todo o processo
      - Duplicatas: Registradas no relatório, mas não impedem outras importações
      `,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Arquivo de planilha com dados dos clientes',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description:
            'Arquivo Excel (.xlsx) ou CSV (.csv) com dados dos clientes',
        },
      },
      required: ['file'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Importação processada com sucesso',

    type: SheetTableImportResultDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Arquivo inválido ou dados incorretos',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example: 'Unsupported file type: txt. Supported types: csv, xlsx',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Token de autenticação inválido ou ausente',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Usuário não tem permissão para importar clientes',
  })
  @ApiResponse({
    status: HttpStatus.PAYLOAD_TOO_LARGE,
    description: 'Arquivo muito grande',
  })
  async sheetTableImport(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: AuthenticatedRequest,
  ): Promise<SheetTableImportResultDto> {
    this.logger.log(`Customer import request from user: ${req?.user?.email}`);

    if (!file) {
      throw new BadRequestException('Arquivo é obrigatório');
    }

    // Validar tipo de arquivo
    const allowedMimeTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new UnsupportedMediaTypeException(
        'Tipo de arquivo não suportado. Use apenas arquivos CSV (.csv) ou Excel (.xlsx)',
      );
    }

    // Validar tamanho do arquivo (máximo 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new BadRequestException(
        'Arquivo muito grande. Tamanho máximo permitido: 10MB',
      );
    }

    // Obter email do usuário do JWT
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    if (!requestUserEmail) {
      throw new UnauthorizedException(
        'Não foi possível identificar o usuário da requisição',
      );
    }

    this.logger.log(
      `Processing import file: ${file.originalname} (${file.size} bytes)`,
    );

    const result = await this.sheetTableImportUseCase.execute({
      file,
      createdBy: requestUserEmail,
    });

    this.logger.log(
      `Import completed for ${file.originalname}. Success: ${result.successCount}, Errors: ${result.errorCount}`,
    );

    return result;
  }
}

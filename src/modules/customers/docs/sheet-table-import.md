# Módulo de Importação de Planilhas (Sheet Table Import)

Este módulo permite a importação em massa de clientes através de planilhas Excel (.xlsx) ou CSV.

## Funcionalidades

- ✅ Upload de planilhas Excel (.xlsx) e CSV
- ✅ Validação de dados da planilha
- ✅ Criação automática de usuários de referência
- ✅ Validação de duplicatas (email e CNPJ)
- ✅ Relatório detalhado de importação
- ✅ Autenticação JWT obrigatória
- ✅ Logs detalhados do processo

## Endpoint

```
POST /core/customers/sheet-table-import
```

### Headers
- `Authorization: Bearer <jwt_token>` (obrigatório)
- `Content-Type: multipart/form-data`

### Body
- `file`: Arquivo da planilha (Excel ou CSV)

### Limites
- Tamanho máximo: 10MB
- Formatos aceitos: .xlsx, .csv

## Formato da Planilha

### ✅ Requisitos do Arquivo
- **Formato**: Excel (.xlsx) ou CSV (.csv)
- **Tamanho máximo**: 10MB
- **Primeira linha**: DEVE conter os cabeçalhos das colunas
- **Ordem das colunas**: Não importa, o sistema identifica pelos nomes dos cabeçalhos
- **Codificação**: UTF-8 (recomendado para CSV)

### 📋 Colunas Aceitas

O sistema aceita múltiplas variações de nomes para cada coluna:

#### **Razão Social** (OBRIGATÓRIO)
- `razaoSocial`, `Razão Social`, `razao_social`, `nome`, `name`

#### **CNPJ** (OBRIGATÓRIO)
- `cnpj`, `CNPJ`
- Formato aceito: com ou sem pontuação (12.345.678/0001-90 ou 12345678000190)

#### **Email** (OBRIGATÓRIO)
- `email`, `Email`
- Será usado para criar o usuário de acesso

#### **Telefone** (OPCIONAL)
- `phone`, `telefone`, `Telefone`

#### **Endereço** (OPCIONAL)
- `street`, `rua`, `endereco`, `Endereço`

#### **Número** (OPCIONAL)
- `number`, `numero`, `Número`

#### **Complemento** (OPCIONAL)
- `complement`, `complemento`, `Complemento`

#### **Cidade** (OPCIONAL)
- `city`, `cidade`, `Cidade`

#### **Estado** (OPCIONAL)
- `state`, `estado`, `Estado`, `uf`, `UF`

#### **CEP** (OPCIONAL)
- `zipCode`, `cep`, `CEP`
- Formato aceito: com ou sem pontuação (01234-567 ou 01234567)

#### **Website** (OPCIONAL)
- `url`, `site`, `Site`, `website`

### 📄 Exemplo de Planilha Excel/CSV

**Primeira linha (cabeçalhos):**
```
razaoSocial | cnpj | email | telefone | street | city | state | zipCode
```

**Dados:**
```
Empresa ABC Ltda | 12.345.678/0001-90 | <EMAIL> | (11) 1234-5678 | Rua das Flores, 123 | São Paulo | SP | 01234-567
Empresa XYZ S.A. | 98.765.432/0001-10 | <EMAIL> | (21) 9876-5432 | Av. Paulista, 456 | Rio de Janeiro | RJ | 98765-432
```

### 📝 Exemplo Alternativo (nomes em português)

**Cabeçalhos:**
```
Razão Social | CNPJ | Email | Telefone | Endereço | Cidade | Estado | CEP
```

**Dados:**
```
Empresa ABC Ltda | 12345678000190 | <EMAIL> | 11 1234-5678 | Rua das Flores, 123 | São Paulo | SP | 01234567
```

## Resposta da API

### Sucesso (200)
```json
{
  "success": true,
  "message": "Importação concluída com sucesso",
  "data": {
    "totalRows": 10,
    "successCount": 8,
    "errorCount": 2,
    "errors": [
      {
        "row": 3,
        "email": "<EMAIL>",
        "error": "Email já existe no sistema"
      },
      {
        "row": 7,
        "cnpj": "12.345.678/0001-90",
        "error": "CNPJ já cadastrado"
      }
    ]
  }
}
```

### Erro (400/401/500)
```json
{
  "success": false,
  "message": "Descrição do erro",
  "error": "Detalhes técnicos do erro"
}
```

## ⚠️ Pontos Importantes

### Headers/Cabeçalhos
- **OBRIGATÓRIO**: A primeira linha deve conter os nomes das colunas
- **Case-insensitive**: `email`, `Email` e `EMAIL` são aceitos
- **Flexível**: Aceita variações como `razaoSocial`, `Razão Social`, `razao_social`

### Dados Obrigatórios
- **razaoSocial**: Nome/Razão social da empresa
- **cnpj**: CNPJ da empresa (com ou sem formatação)
- **email**: Email válido (será usado para criar usuário)
- **telefone**: Telefone de contato

### Formatação Automática
- **CNPJ**: Remove automaticamente pontuação (12.345.678/0001-90 → 12345678000190)
- **CEP**: Remove automaticamente pontuação (01234-567 → 01234567)
- **Email**: Converte automaticamente para minúsculas

## Validações Realizadas

1. **Arquivo**: Formato, tamanho e integridade
2. **Dados obrigatórios**: Razão social, email, CNPJ e telefone
3. **Formato de email**: Validação de formato válido
4. **CNPJ**: Validação de formato e dígitos verificadores
5. **Duplicatas**: Verificação de email e CNPJ já existentes
6. **Usuário**: Validação do usuário que está fazendo a importação

## Processo de Importação

1. **Validação do usuário**: Verifica se o usuário do JWT existe no sistema
2. **Leitura da planilha**: Processa o arquivo e extrai os dados
3. **Validação dos dados**: Valida cada linha da planilha
4. **Criação de usuários**: Cria usuários de referência para cada cliente
5. **Criação de clientes**: Cria os registros de clientes no banco
6. **Relatório**: Gera relatório com sucessos e erros

## Logs

O sistema gera logs detalhados durante todo o processo:
- Início da importação
- Validação de usuário
- Processamento de cada linha
- Erros encontrados
- Resultado final

## Tratamento de Erros

- **Erros de validação**: Continuam processando outras linhas
- **Erros críticos**: Interrompem todo o processo
- **Duplicatas**: Registradas no relatório, mas não impedem outras importações

## Segurança

- Autenticação JWT obrigatória
- Validação do usuário criador
- Logs de auditoria
- Validação de tipos de arquivo
- Limite de tamanho de arquivo

## Arquivos do Módulo

### Controllers
- `src/modules/customers/application/controllers/sheet-table-import.controller.ts`
- `src/modules/customers/application/controllers/sheet-table-import.controller.spec.ts`

### Use Cases
- `src/modules/customers/application/use-cases/sheet-table-import.use-case.ts`
- `src/modules/customers/application/use-cases/sheet-table-import.use-case.spec.ts`

### DTOs
- `src/modules/customers/infrastructure/dtos/sheet-table-import.dto.ts`

### Documentação
- `src/modules/customers/docs/sheet-table-import.md`
- `src/modules/customers/docs/exemplo-planilha.csv`

## Dependências

- FileReaderModule: Para leitura de planilhas
- UsersModule: Para criação de usuários
- AuthModule: Para autenticação
- PrismaModule: Para persistência de dados

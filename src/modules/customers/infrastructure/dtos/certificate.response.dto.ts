import { ApiProperty } from '@nestjs/swagger';
import { Certificate } from '../../domain/entities/certificate.entity';
import { CertificateCategory } from '../../domain/enums/certificate-category.enum';
import { CertificateType } from '../../domain/enums/certificate-type.enum';

export class CertificateResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  customerId: number;

  @ApiProperty({ enum: CertificateCategory })
  category: CertificateCategory;

  @ApiProperty({ enum: CertificateType })
  type: CertificateType;

  @ApiProperty()
  fileUrl: string;

  @ApiProperty({ required: false })
  notes?: string;

  @ApiProperty({ required: false })
  emissionDate?: Date;

  @ApiProperty({ required: false })
  expirationDate?: Date;

  @ApiProperty()
  uploadedById: string;

  @ApiProperty({ required: false })
  uploadedByName?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty({
    required: false,
    description:
      'URL assinada para download do certificado (válida por 1 hora)',
  })
  fullFileUrl?: string;

  static fromEntity(entity: Certificate): CertificateResponseDto {
    return {
      id: entity.id,
      customerId: entity.customerId,
      category: entity.category,
      type: entity.type,
      fileUrl: entity.fileUrl,
      notes: entity.notes,
      emissionDate: entity.emissionDate,
      expirationDate: entity.expirationDate,
      uploadedById: entity.uploadedById,
      createdAt: entity.createdAt,
      uploadedByName: entity.uploadedByName,
    };
  }
}

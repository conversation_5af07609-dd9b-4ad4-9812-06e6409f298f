import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEmail,
  Matches,
  MinLength,
  MaxLength,
} from 'class-validator';

export class SheetTableImportRowDto {
  @ApiProperty({
    description: 'Razão Social da empresa',
    example: 'ACME Corporation LTDA',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(255)
  razaoSocial: string;

  @ApiProperty({
    description: 'CNPJ da empresa (apenas números)',
    example: '12345678901234',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{14}$/, {
    message: 'CNPJ deve conter exatamente 14 dígitos numéricos',
  })
  cnpj: string;

  @ApiProperty({
    description: 'Email da empresa',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Telefone da empresa',
    example: '11999999999',
    required: false,
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'URL/Site da empresa',
    example: 'https://www.acme.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  url?: string;

  // Campos de endereço
  @ApiProperty({
    description: 'Rua/Logradouro',
    example: 'Rua das Flores, 123',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(5)
  @MaxLength(255)
  street: string;

  @ApiProperty({
    description: 'Número do endereço',
    example: '123',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  number?: string;

  @ApiProperty({
    description: 'Complemento do endereço',
    example: 'Sala 101',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  complement?: string;

  @ApiProperty({
    description: 'Bairro',
    example: 'Centro',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(3, {
    message: 'Bairro deve ter pelo menos 3 caracteres quando informado',
  })
  @MaxLength(50)
  neighborhood?: string;

  @ApiProperty({
    description: 'Cidade',
    example: 'São Paulo',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  city: string;

  @ApiProperty({
    description: 'Estado (sigla)',
    example: 'SP',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[A-Z]{2}$/, {
    message: 'Estado deve ser uma sigla de 2 letras maiúsculas',
  })
  state: string;

  @ApiProperty({
    description: 'CEP (apenas números)',
    example: '01311000',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{8}$/, {
    message: 'CEP deve conter exatamente 8 dígitos numéricos',
  })
  zipCode: string;
}

export class SheetTableImportResultDto {
  @ApiProperty({ description: 'Total de linhas processadas' })
  totalProcessed: number;

  @ApiProperty({ description: 'Total de clientes criados com sucesso' })
  successCount: number;

  @ApiProperty({ description: 'Total de erros encontrados' })
  errorCount: number;

  @ApiProperty({ description: 'Lista de erros por linha', type: [String] })
  errors: string[];

  @ApiProperty({
    description: 'Lista de clientes criados com sucesso',
    type: [String],
  })
  successfulCustomers: string[];
}

export interface SheetTableImportError {
  row: number;
  field?: string;
  message: string;
  data?: any;
}

import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '../../../../infrastructure/prisma/prisma.service';
import { CustomerRepository } from './customer.repository';
import { CustomerStatus } from '../../domain/entities/customer.entity';

// Interface para o mock do PrismaService
interface MockPrismaService {
  customer: {
    create: jest.Mock;
    findUnique: jest.Mock;
    findFirst: jest.Mock;
    findMany: jest.Mock;
    update: jest.Mock;
    delete: jest.Mock;
    count: jest.Mock;
  };
  $transaction: jest.Mock;
}

describe('CustomerRepository', () => {
  let repository: CustomerRepository;
  let prismaService: MockPrismaService;

  beforeEach(async () => {
    // Mock do PrismaService com tipagem adequada
    const mockPrismaService: MockPrismaService = {
      customer: {
        create: jest.fn(),
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        findMany: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        count: jest.fn(),
      },
      $transaction: jest
        .fn()
        .mockImplementation(<T>(callback: () => T): T => callback()),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<CustomerRepository>(CustomerRepository);
    prismaService = module.get(PrismaService);
  });

  it('should soft delete a customer', async () => {
    // Arrange
    const mockCustomer = {
      id: 1,
      uuid: 'test-uuid',
      razaoSocial: 'Test Company',
      cnpj: '12345678901234',
      email: '<EMAIL>',
      phone: '11999999999',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    const softDeletedCustomer = {
      ...mockCustomer,
      deletedAt: new Date(),
    };

    // Mock the create method
    prismaService.customer.create.mockResolvedValue(mockCustomer);

    // Mock the delete method (soft delete)
    prismaService.customer.update.mockResolvedValue(softDeletedCustomer);

    // Mock the findByUuid method to return the soft deleted customer
    prismaService.customer.findUnique.mockResolvedValue(softDeletedCustomer);

    // Mock the findByDocument method to return null (soft deleted customers are not found)
    prismaService.customer.findFirst.mockResolvedValueOnce(null);

    // Mock the listCustomers method to return empty list
    prismaService.customer.findMany.mockResolvedValue([]);
    prismaService.customer.count.mockResolvedValue(0);

    // Act
    const created = await repository.create({
      uuid: 'test-uuid',
      razaoSocial: 'Test Company',
      cnpj: '12345678901234',
      email: '<EMAIL>',
      phone: '11999999999',
      status: CustomerStatus.ACTIVE,
      userId: 'user1',
      url: 'https://test.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await repository.delete(created.uuid);
    const byUuid = await repository.findByUuid(created.uuid);
    const byDoc = await repository.findByDocument(created.cnpj);
    const list = await repository.listCustomers({}, 10, 0);

    // Assert
    expect(byUuid).toBeDefined();
    expect(byUuid?.deletedAt).toBeInstanceOf(Date);
    expect(byDoc).toBeNull();
    expect(list.total).toBe(0);
  });

  it('should update a customer', async () => {
    // Arrange
    const mockCustomer = {
      id: 2,
      uuid: 'patch-uuid',
      razaoSocial: 'Old Company Name',
      cnpj: '99999999999999',
      email: '<EMAIL>',
      phone: '11888888888',
      status: CustomerStatus.ACTIVE,
      userId: 'user2',
      url: 'https://old.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    const updatedCustomer = {
      ...mockCustomer,
      razaoSocial: 'New Company Name',
      email: '<EMAIL>',
      updatedBy: 'admin',
      updatedAt: new Date(),
    };

    // Mock the create method
    prismaService.customer.create.mockResolvedValue(mockCustomer);

    // Mock the update method
    prismaService.customer.update.mockResolvedValue(updatedCustomer);

    // Act
    const created = await repository.create({
      uuid: 'patch-uuid',
      razaoSocial: 'Old Company Name',
      cnpj: '99999999999999',
      email: '<EMAIL>',
      phone: '11888888888',
      status: CustomerStatus.ACTIVE,
      userId: 'user2',
      url: 'https://old.com',
      createdBy: 'admin',
      updatedBy: 'admin',
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    const updated = await repository.update(created.uuid, {
      razaoSocial: 'New Company Name',
      email: '<EMAIL>',
      updatedBy: 'admin',
    });

    // Assert
    expect(updated.razaoSocial).toBe('New Company Name');
    expect(updated.email).toBe('<EMAIL>');
    expect(updated.uuid).toBe(created.uuid);
  });
});

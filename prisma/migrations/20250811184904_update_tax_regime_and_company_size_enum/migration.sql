/*
  Warnings:

  - The values [MI<PERSON><PERSON>MPRE<PERSON>,PEQUENO_PORTE,MEDIO_PORTE,GRANDE_PORTE] on the enum `CompanySize` will be removed. If these variants are still used in the database, this will fail.
  - The values [MEI] on the enum `TaxRegime` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "core"."CompanySize_new" AS ENUM ('MEI', 'ME', 'EPP', 'MEDIUM', 'LARGE');
ALTER TABLE "core"."suppliers" ALTER COLUMN "companySize" TYPE "core"."CompanySize_new" USING ("companySize"::text::"core"."CompanySize_new");
ALTER TYPE "core"."CompanySize" RENAME TO "CompanySize_old";
ALTER TYPE "core"."CompanySize_new" RENAME TO "CompanySize";
DROP TYPE "core"."CompanySize_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "core"."TaxRegime_new" AS ENUM ('SIMPLES_NACIONAL', 'SIMPLES_MEI', 'LUCRO_PRESUMIDO', 'LUCRO_REAL', 'LUCRO_ARBITRADO');
ALTER TABLE "core"."suppliers" ALTER COLUMN "taxRegime" TYPE "core"."TaxRegime_new" USING ("taxRegime"::text::"core"."TaxRegime_new");
ALTER TYPE "core"."TaxRegime" RENAME TO "TaxRegime_old";
ALTER TYPE "core"."TaxRegime_new" RENAME TO "TaxRegime";
DROP TYPE "core"."TaxRegime_old";
COMMIT;

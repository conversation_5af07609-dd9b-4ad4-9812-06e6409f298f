---
type: "manual"
---

GIT
1. Visão Geral
O fluxo de trabalho é baseado no modelo Gitflow, adaptado para atender aos requisitos de
deploy em homologação (ao fazer merge na branch HOMOLOG) e produção (ao fazer merge
na master). O fluxo prioriza a estabilidade do código e a rastreabilidade das alterações, com
o merge de HOMOLOG para master sendo o caminho recomendado para deploys em
produção.
Princípios do Fluxo
● Branch principal (master): Representa o código em produção. Todo merge na
master dispara um deploy automático para o ambiente de produção (PRD).
● Branch de integração (HOMOLOG): Usada para integrar e testar as alterações antes
de irem para produção. Todo merge na HOMOLOG dispara um deploy automático para
o ambiente de homologação.
● Branches de trabalho: Criadas a partir da master para desenvolvimento de novas
funcionalidades, correções de bugs ou outras tarefas.
● Merge recomendado: O deploy em produção deve, idealmente, ser feito por meio
de um merge da branch HOMOLOG para a master, garantindo que o código tenha sido
previamente validado em homologação.
● Commits semânticos: Todos os commits devem ser escritos em inglês, seguindo o
padrão de Conventional Commits (ex.: feat: add user authentication, fix:
resolve login error).
2. Estrutura das Branches
2.1. Branches Permanentes
● master: Contém o código estável em produção. Qualquer merge nesta branch
resulta em um deploy automático para o ambiente de produção (PRD).
● HOMOLOG: Contém o código integrado para testes no ambiente de homologação.
Qualquer merge nesta branch resulta em um deploy automático para o ambiente de
homologação.
2.2. Branches Temporárias
● feature/*: Usada para desenvolver novas funcionalidades. Criada a partir da
master e mergeada na HOMOLOG após conclusão.
● fix/*: Usada para corrigir bugs. Criada a partir da master e mergeada na HOMOLOG
após conclusão.
● hotfix/*: Usada para correções urgentes que precisam ir diretamente para
produção. Criada a partir da master e mergeada diretamente na master (e,
opcionalmente, na HOMOLOG para manter a consistência).
● release/*: Usada para preparar um deploy em produção, geralmente a partir da
HOMOLOG. Após validação, é mergeada na master para o deploy em PRD.
3. Fluxo de Trabalho
3.1. Desenvolvimento de Novas Funcionalidades
● Criar uma branch de trabalho:
● A partir da master, crie uma branch com o prefixo feature/ (ex.:
feature/user-authentication).
● Exemplo: git checkout master && git pull && git checkout -b
feature/user-authentication
● Desenvolver a funcionalidade:
● Faça commits em inglês, seguindo o padrão semântico.
● Exemplo: git commit -m "feat: add user authentication form"
● Merge na HOMOLOG:
● Após concluir o desenvolvimento e testes locais, crie um Pull Request (PR)
da branch feature/user-authentication para a HOMOLOG.
● Passe por revisão de código e testes adicionais, se necessário.
● Após aprovação, faça o merge na HOMOLOG.
● Exemplo: git checkout HOMOLOG && git pull && git merge --no-ff
feature/user-authentication
● Deploy em homologação:
● O merge na HOMOLOG dispara automaticamente o deploy no ambiente de
homologação.
● Realize os testes necessários no ambiente de homologação.
● Excluir a branch de trabalho:
● Após o merge, exclua a branch temporária.
● Exemplo: git branch -d feature/user-authentication
3.2. Correção de Bugs
● Criar uma branch de correção:
● A partir da master, crie uma branch com o prefixo fix/ (ex.:
fix/login-error).
● Exemplo: git checkout master && git pull && git checkout -b
fix/login-error
● Corrigir o bug:
● Faça commits em inglês, seguindo o padrão semântico.
● Exemplo: git commit -m "fix: resolve login error on invalid
credentials"
● Merge na HOMOLOG:
● Crie um Pull Request da branch fix/login-error para a HOMOLOG.
● Após revisão e aprovação, faça o merge na HOMOLOG.
● Deploy em homologação:
● O merge na HOMOLOG dispara o deploy automático no ambiente de
homologação.
● Valide as correções no ambiente de homologação.
● Excluir a branch de correção:
● Após o merge, exclua a branch temporária.
● Exemplo: git branch -d fix/login-error
3.3. Deploy em Produção (Caminho Recomendado)
● Preparar a release:
● Após validação no ambiente de homologação, crie uma branch de release a
partir da HOMOLOG (ex.: release/v1.2.0).
● Exemplo: git checkout HOMOLOG && git pull && git checkout -b
release/v1.2.0
● Realizar ajustes finais (se necessário):
● Faça ajustes como atualização de versão ou pequenos fixes, usando
commits semânticos.
● Exemplo: git commit -m "chore: update version to 1.2.0"
● Merge na master:
● Crie um Pull Request da branch release/v1.2.0 para a master.
● Após revisão e aprovação, faça o merge na master.
● Exemplo: git checkout master && git pull && git merge --no-ff
release/v1.2.0
● Deploy em produção:
● O merge na master dispara automaticamente o deploy no ambiente de
produção (PRD).
● Sincronizar a HOMOLOG:
● Para manter a consistência, faça o merge da release/v1.2.0 na HOMOLOG.
● Exemplo: git checkout HOMOLOG && git pull && git merge --no-ff
release/v1.2.0
● Excluir a branch de release:
● Após o merge, exclua a branch temporária.
● Exemplo: git branch -d release/v1.2.0
3.4. Correções Urgentes (Hotfix)
● Criar uma branch de hotfix:
● A partir da master, crie uma branch com o prefixo hotfix/ (ex.:
hotfix/critical-payment-bug).
● Exemplo: git checkout master && git pull && git checkout -b
hotfix/critical-payment-bug
● Corrigir o problema:
● Faça commits em inglês, seguindo o padrão semântico.
● Exemplo: git commit -m "fix: resolve critical payment processing
error"
● Merge na master:
● Crie um Pull Request da branch hotfix/critical-payment-bug para a
master.
● Após revisão e aprovação, faça o merge na master.
● Exemplo: git checkout master && git pull && git merge --no-ff
hotfix/critical-payment-bug
● Deploy em produção:
● O merge na master dispara o deploy automático no ambiente de produção.
● Sincronizar a HOMOLOG:
● Para manter a consistência, faça o merge da branch
hotfix/critical-payment-bug na HOMOLOG.
● Exemplo: git checkout HOMOLOG && git pull && git merge --no-ff
hotfix/critical-payment-bug
● Excluir a branch de hotfix:
● Após o merge, exclua a branch temporária.
● Exemplo: git branch -d hotfix/critical-payment-bug
4. Regras e Boas Práticas
● Nomenclatura das branches:
● Use nomes descritivos e padronizados: feature/nome-da-funcionalidade,
fix/descricao-do-bug, hotfix/descricao-do-problema, release/vX.Y.Z.
● Commits semânticos:
● Todos os commits devem ser escritos em inglês, seguindo o padrão
Conventional Commits.
● Exemplos de tipos de commit:
● feat: Nova funcionalidade (ex.: feat: add user profile page).
● fix: Correção de bug (ex.: fix: resolve null pointer exception
in login).
● chore: Tarefas de manutenção (ex.: chore: update dependencies).
● docs: Alterações em documentação (ex.: docs: update README with
new instructions).
● test: Adição ou modificação de testes (ex.: test: add unit tests
for login).
● refactor: Refatoração de código sem alterar comportamento (ex.:
refactor: simplify user validation logic).
● Estrutura: <tipo>(<escopo>): <descrição curta> (ex.: feat(auth):
implement JWT-based authentication).
● Pull Requests:
● Sempre passe por revisão de código antes de realizar merges.
● Inclua descrições claras nos PRs, explicando o propósito das alterações.
● Testes:
● Realize testes locais antes de enviar para HOMOLOG.
● Valide as alterações no ambiente de homologação antes de aprovar o merge
para master.
● Manutenção da HOMOLOG:
● Mantenha a HOMOLOG atualizada com as alterações da master após cada
deploy em produção.
● Evitar merges diretos na master:
● Exceto em casos de hotfix, o merge na master deve ser feito a partir da
HOMOLOG ou de uma branch release/* para garantir que o código foi testado
em homologação.
5. Exemplos de Commits Semânticos
● feat: add user registration endpoint
● fix: correct validation error in login form
● chore: update npm dependencies
● docs: add Gitflow documentation
● test: add unit tests for payment processing
● refactor: simplify database query logic
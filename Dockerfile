# syntax=docker/dockerfile:1

FROM node:22-alpine AS builder

# Set the working directory
WORKDIR /app

ARG GITHUB_TOKEN
RUN apk add --no-cache git openssh

# Copia arquivos de dependência
COPY package*.json ./

RUN if [ -n "$GITHUB_TOKEN" ]; then \
      sed -i "s#git+ssh://git@github\.com[:/]#git+https://${GITHUB_TOKEN}@github.com/#g" package.json package-lock.json ; \
    fi

RUN --mount=type=ssh \
    if [ -z "$GITHUB_TOKEN" ]; then \
        mkdir -p ~/.ssh && \
        ssh-keyscan github.com >> ~/.ssh/known_hosts && \
        git --no-replace-objects ls-remote ssh://**************/PetrusSoftware/backoffice-backend-common.git ; \
    fi

RUN npm install -g npm && npm install

# Copia código fonte
COPY . .

# Gera o cliente Prisma e compila o código
RUN npx prisma generate

# Stage development: Prepare the runtime image
FROM node:22-alpine AS development

# Set the working directory
WORKDIR /app

# Copy the built application from the builder stage
COPY --from=builder /app /app

# Run the application using pnpm
CMD ["sh", "-c", "npx prisma migrate deploy && npm run start"]

# Stage de produção
FROM node:22-alpine AS production

WORKDIR /app

# Define argumentos e variáveis de ambiente
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

# Copia apenas o necessário para produção
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/prisma ./prisma

COPY --from=builder /app /app


# Build the application
RUN npm run build

# Define o ponto de entrada
CMD ["sh", "-c", "npx prisma migrate deploy && npm run start:prod"]